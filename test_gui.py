#!/usr/bin/env python3
"""
Test script for the GUI without mat<PERSON><PERSON>lib to isolate the issue.
"""

import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap
import logging

# Import our IR reader
from irpkg_reader import IRPKGReader

class SimpleIRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist (Dataset Version)")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # Add title
        title = QLabel("IR-Alchemist - Dataset Version")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #000000;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Add style selection
        style_group = QGroupBox("IR Style")
        style_layout = QHBoxLayout()
        
        self.style_buttons = QButtonGroup()
        self.selected_style = "American"
        
        for style in ["American", "British", "German", "Random"]:
            btn = QRadioButton(style)
            if style == "American":
                btn.setChecked(True)
            btn.clicked.connect(lambda checked, s=style: self.update_style(s))
            self.style_buttons.addButton(btn)
            style_layout.addWidget(btn)
        
        style_group.setLayout(style_layout)
        layout.addWidget(style_group)
        
        # Add generate button
        self.generate_btn = QPushButton("Generate 10 IRs")
        self.generate_btn.setFixedHeight(50)
        self.generate_btn.clicked.connect(self.generate_irs)
        layout.addWidget(self.generate_btn)
        
        # Add status label
        self.status_label = QLabel("Ready to generate IRs")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
        
        # Initialize IR exporter
        try:
            from test_ir_exporter import IRExporter
            self.exporter = IRExporter()
            self.status_label.setText(f"Ready - Loaded {len(self.exporter.ir_reader.irs)} IRs from dataset")
        except Exception as e:
            self.status_label.setText(f"Error loading IR dataset: {e}")
            self.generate_btn.setEnabled(False)
    
    def update_style(self, style):
        if self.sender().isChecked():
            self.selected_style = style
            self.status_label.setText(f"Selected style: {style}")
    
    def generate_irs(self):
        try:
            self.generate_btn.setEnabled(False)
            self.status_label.setText("Generating IRs...")
            
            # Generate IRs
            irs = self.exporter.generate_multiple_irs(count=10, style=self.selected_style)
            
            self.status_label.setText(f"Generated {len(irs)} IRs for {self.selected_style} style")
            
            # Show message box with results
            QMessageBox.information(self, "Success", 
                                  f"Successfully generated {len(irs)} IRs for {self.selected_style} style!\n"
                                  f"Each IR has {irs[0].shape[0]} samples.")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error generating IRs: {e}")
            self.status_label.setText(f"Error: {e}")
        finally:
            self.generate_btn.setEnabled(True)

def main():
    try:
        app = QApplication(sys.argv)
        
        # Load font
        font_path = os.path.join(os.path.dirname(__file__), "Exo-VariableFont_wght.ttf")
        if os.path.exists(font_path):
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id != -1:
                families = QFontDatabase.applicationFontFamilies(font_id)
                if families:
                    default_font = QFont(families[0])
                    app.setFont(default_font)
                    print("Loaded font:", families[0])
        
        print("Creating GUI...")
        window = SimpleIRGeneratorGUI()
        print("Showing window...")
        window.show()
        print("Starting application...")
        
        return app.exec_()
        
    except Exception as e:
        print(f"GUI Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
