#!/usr/bin/env python3
"""
IR-Alchemist - Modern Material Design Version
Professional Cabinet IR Generator with Stereo Audio Support
"""

import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect,
    QComboBox, QProgressBar, QStatusBar, QMenuBar, QAction, QToolBar, QScrollArea
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap, QKeySequence
import logging

# Import modern UI framework and stereo audio handler
from modern_ui_framework import *
from stereo_audio_handler import stereo_handler
from native_visualization import NativeDetailCanvas as DetailCanvas

# Using native PyQt5 visualization system
logging.info("Using native PyQt5 visualization system")

# Import the IR reader
from irpkg_reader import IRPKGReader

class IRExporter:
    def __init__(self, package_file=None):
        # Import the IR reader
        from irpkg_reader import IRPKGReader
        
        # Initialize the IR dataset reader with packaged dataset
        self.ir_reader = IRPKGReader(package_file)

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """
        Generate multiple IRs by randomly selecting from the dataset without any processing.
        
        Args:
            diversity (float): Ignored - kept for interface compatibility
            count (int): Number of IRs to return (default: 10)
            style (str): Ignored - random selection from entire dataset
            
        Returns:
            list: List of raw, unprocessed IR arrays from the dataset
        """
        # Ensure we have IRs available
        if not self.ir_reader.irs:
            logging.warning("No IRs available in dataset")
            return []
        
        # Calculate how many unique IRs we can provide
        total_irs = len(self.ir_reader.irs)
        unique_count = min(count, total_irs)
        
        # Randomly select unique IRs from the entire dataset (ignore style)
        import random
        selected_indices = random.sample(range(total_irs), unique_count)
        
        # Get the raw IRs without any processing
        raw_irs = [self.ir_reader.irs[i].copy() for i in selected_indices]
        
        # If we need more IRs than unique ones available, fill with additional random selections
        if count > unique_count:
            additional_needed = count - unique_count
            for _ in range(additional_needed):
                # Select random IR (may be duplicate now)
                random_index = random.randint(0, total_irs - 1)
                raw_irs.append(self.ir_reader.irs[random_index].copy())
        
        logging.info(f"Selected {len(raw_irs)} raw IRs from dataset ({unique_count} unique)")
        return raw_irs

class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, exporter, style, count):
        super().__init__()
        self.exporter = exporter
        self.style = style
        self.count = count

    @pyqtSlot()
    def run(self):
        try:
            irs = self.exporter.generate_multiple_irs(count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            self.error.emit(str(e))

class ModernIRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist - Professional Audio Processing")
        self.setGeometry(100, 100, 1600, 1000)

        # Apply Material Design theme
        MaterialTheme.apply_global_style(QApplication.instance())

        # Initialize data
        self.last_generated_irs = None
        self.selected_style = "American"
        self.selected_ir_indices = set()  # Multi-selection support
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.worker = None
        self.current_temp_file = None

        # Initialize responsive grid layout
        self.responsive_layout = None

        self.player.mediaStatusChanged.connect(self._on_media_status_changed)

        # Create modern UI
        self.create_modern_interface()

        # Initialize the exporter
        self.initialize_exporter()

    def resizeEvent(self, event):
        """Handle window resize for responsive design."""
        super().resizeEvent(event)
        if self.responsive_layout:
            self.responsive_layout.update_layout_for_breakpoint(self.width())

            # Update responsive styles based on current breakpoint
            current_breakpoint = self.responsive_layout.get_current_breakpoint(self.width())
            MaterialTheme.update_responsive_styles(self, current_breakpoint)
    
    def create_modern_interface(self):
        """Create a 2-column interface with proper sizing and multi-selection support."""
        # Set main window background
        self.setStyleSheet(f"background-color: {MaterialColors.BACKGROUND};")

        # Create main central widget with proper layout
        central_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)

        # Create header (compact, at top)
        header_widget = self.create_functional_header()
        main_layout.addWidget(header_widget)

        # Create the two main panels
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)

        # Left panel: Generated IRs with multi-selection
        generated_irs_panel = self.create_generated_irs_panel_with_multiselect()

        # Right panel: Analysis and Generation combined
        analysis_generation_panel = self.create_analysis_generation_panel()

        # Add panels with proper sizing (50/50 split)
        content_layout.addWidget(generated_irs_panel, 1)     # Generated IRs - left side
        content_layout.addWidget(analysis_generation_panel, 1) # Analysis & Generation - right side

        main_layout.addLayout(content_layout)

        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

        # Create status bar
        self.create_status_bar()

    def create_generated_irs_panel_with_multiselect(self):
        """Create the Generated IRs panel with multi-selection support."""
        main_panel = MaterialCard(elevation=2)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(20)

        # Header with title and selection info
        header_layout = QHBoxLayout()
        header_layout.setSpacing(16)

        title_label = QLabel("Generated IRs")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            margin: 0;
            padding: 0;
        """)

        self.selection_info_label = QLabel("No IRs generated")
        self.selection_info_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            background-color: {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
            padding: 6px 12px;
            margin: 0;
        """)
        self.selection_info_label.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.selection_info_label)
        main_layout.addLayout(header_layout)

        # Selection controls
        selection_controls = QHBoxLayout()
        selection_controls.setSpacing(12)

        self.select_all_button = MaterialButton("Select All", "outlined")
        self.select_all_button.setMinimumHeight(36)
        self.select_all_button.clicked.connect(self.select_all_irs)
        self.select_all_button.setEnabled(False)

        self.clear_selection_button = MaterialButton("Clear Selection", "text")
        self.clear_selection_button.setMinimumHeight(36)
        self.clear_selection_button.clicked.connect(self.clear_ir_selection)
        self.clear_selection_button.setEnabled(False)

        selection_controls.addWidget(self.select_all_button)
        selection_controls.addWidget(self.clear_selection_button)
        selection_controls.addStretch()
        main_layout.addLayout(selection_controls)

        # IR list area with scroll
        self.ir_list_widget = QWidget()
        self.ir_list_widget.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE};
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
        """)

        self.ir_list_layout = QVBoxLayout()
        self.ir_list_layout.setContentsMargins(12, 12, 12, 12)
        self.ir_list_layout.setSpacing(8)
        self.ir_list_widget.setLayout(self.ir_list_layout)

        # Add placeholder
        self.add_ir_placeholder()

        # Scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.ir_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(400)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {MaterialColors.ON_SURFACE_VARIANT};
                border-radius: 6px;
                min-height: 20px;
            }}
        """)
        main_layout.addWidget(scroll_area, 1)

        # Export section
        export_card = self.create_export_section()
        main_layout.addWidget(export_card)

        main_panel.setLayout(main_layout)
        return main_panel

    def create_analysis_generation_panel(self):
        """Create combined analysis and generation panel."""
        main_panel = MaterialCard(elevation=1)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(20)

        # Generation section
        gen_section = self.create_generation_section_compact()
        main_layout.addWidget(gen_section)

        # Analysis section
        analysis_section = self.create_analysis_section()
        main_layout.addWidget(analysis_section, 1)  # Takes remaining space

        main_panel.setLayout(main_layout)
        return main_panel

    def create_generation_section_compact(self):
        """Create compact generation section."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER};
            border: 1px solid {MaterialColors.PRIMARY};
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 20, 20, 20)
        section_layout.setSpacing(16)

        # Title
        title_label = QLabel("IR Generation")
        title_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 600;
            color: {MaterialColors.PRIMARY};
            margin: 0;
            padding: 0 0 8px 0;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        section_layout.addWidget(title_label)

        # Style selection
        style_group = QGroupBox("Sound Style")
        style_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: 500;
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 8px;
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.PRIMARY};
            }}
        """)

        style_layout = QHBoxLayout()  # Horizontal layout for compact design
        style_layout.setSpacing(12)

        # Create radio buttons
        self.style_group = QButtonGroup()
        styles = ["American", "British", "German", "Random"]

        for style in styles:
            radio = QRadioButton(style)
            radio.setStyleSheet(f"""
                QRadioButton {{
                    font-size: 12px;
                    color: {MaterialColors.ON_SURFACE};
                    spacing: 6px;
                    padding: 4px;
                }}
                QRadioButton::indicator {{
                    width: 14px;
                    height: 14px;
                    border-radius: 7px;
                    border: 2px solid {MaterialColors.ON_SURFACE_VARIANT};
                    background-color: transparent;
                }}
                QRadioButton::indicator:checked {{
                    background-color: {MaterialColors.PRIMARY};
                    border: 2px solid {MaterialColors.PRIMARY};
                }}
                QRadioButton::indicator:hover {{
                    border: 2px solid {MaterialColors.PRIMARY};
                }}
            """)

            if style == "American":
                radio.setChecked(True)
                self.selected_style = style

            radio.toggled.connect(lambda checked, s=style: self.on_style_changed(s, checked) if checked else None)
            self.style_group.addButton(radio)
            style_layout.addWidget(radio)

        style_group.setLayout(style_layout)
        section_layout.addWidget(style_group)

        # Generate button
        self.generate_button = MaterialButton("🎵 Generate All IRs", "filled")
        self.generate_button.setMinimumHeight(48)
        self.generate_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 24px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.PRIMARY_VARIANT};
            }}
            QPushButton:disabled {{
                background-color: {MaterialColors.DISABLED};
                color: {MaterialColors.ON_SURFACE_VARIANT};
            }}
        """)
        self.generate_button.clicked.connect(self.generate_irs)
        section_layout.addWidget(self.generate_button)

        # Progress bar
        self.progress_bar = MaterialProgressBar()
        self.progress_bar.setVisible(False)
        section_layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready to generate IRs")
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            margin: 0;
            padding: 4px 0;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setWordWrap(True)
        section_layout.addWidget(self.status_label)

        section_card.setLayout(section_layout)
        return section_card

    def create_analysis_section(self):
        """Create analysis section with visualization."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 20, 20, 20)
        section_layout.setSpacing(16)

        # Title
        title_label = QLabel("IR Analysis & Visualization")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            margin: 0;
            padding: 0 0 8px 0;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        section_layout.addWidget(title_label)

        # Visualization canvas
        self.detail_canvas = DetailCanvas()
        self.detail_canvas.setStyleSheet(f"""
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
            background-color: {MaterialColors.SURFACE};
        """)
        self.detail_canvas.setMinimumHeight(300)
        section_layout.addWidget(self.detail_canvas, 1)

        section_card.setLayout(section_layout)
        return section_card

    def create_export_section(self):
        """Create export section with multi-selection support."""
        export_card = MaterialCard(elevation=1)
        export_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
            border: 2px solid {MaterialColors.PRIMARY};
        """)

        export_layout = QVBoxLayout()
        export_layout.setContentsMargins(20, 16, 20, 16)
        export_layout.setSpacing(12)

        export_title = QLabel("Export Options")
        export_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 600;
            color: {MaterialColors.PRIMARY};
            margin: 0;
            padding: 0 0 4px 0;
        """)
        export_title.setAlignment(Qt.AlignCenter)
        export_layout.addWidget(export_title)

        # Export buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(12)

        self.export_selected_button = MaterialButton("📁 Export Selected", "filled")
        self.export_selected_button.setMinimumHeight(44)
        self.export_selected_button.clicked.connect(self.export_selected_irs)
        self.export_selected_button.setEnabled(False)

        self.export_combined_button = MaterialButton("🔗 Export Combined", "outlined")
        self.export_combined_button.setMinimumHeight(44)
        self.export_combined_button.clicked.connect(self.export_combined_ir)
        self.export_combined_button.setEnabled(False)

        button_layout.addWidget(self.export_selected_button)
        button_layout.addWidget(self.export_combined_button)
        export_layout.addLayout(button_layout)

        # Preview button
        self.play_button = MaterialButton("▶ Preview Selected", "text")
        self.play_button.setMinimumHeight(36)
        self.play_button.clicked.connect(self.preview_selected_irs)
        self.play_button.setEnabled(False)
        export_layout.addWidget(self.play_button)

        export_card.setLayout(export_layout)
        return export_card

    def create_functional_header(self):
        """Create a functional, clean header."""
        header_card = MaterialCard(elevation=1)
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(24, 16, 24, 16)
        header_layout.setSpacing(20)

        # Title
        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            margin: 0;
            padding: 0;
        """)

        # Subtitle
        subtitle_label = QLabel("Professional Cabinet IR Generator")
        subtitle_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            margin: 0;
            padding: 0;
        """)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addStretch()

        header_card.setLayout(header_layout)
        header_card.setFixedHeight(60)
        return header_card





    def create_functional_controls_panel(self):
        """Create a functional controls panel with working buttons."""
        controls_card = MaterialCard(elevation=1)
        controls_card.setFixedWidth(300)
        controls_layout = QVBoxLayout()
        controls_layout.setContentsMargins(20, 24, 20, 24)
        controls_layout.setSpacing(20)

        # Title
        title_label = QLabel("Controls")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.PRIMARY};
            margin: 0;
            padding: 0 0 8px 0;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        controls_layout.addWidget(title_label)

        # Style selection
        style_group = QGroupBox("Sound Style")
        style_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: 500;
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 8px;
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.PRIMARY};
            }}
        """)

        style_layout = QVBoxLayout()
        style_layout.setSpacing(8)

        # Create radio buttons with proper functionality
        self.style_group = QButtonGroup()
        styles = ["American", "British", "German", "Random"]

        for style in styles:
            radio = QRadioButton(style)
            radio.setStyleSheet(f"""
                QRadioButton {{
                    font-size: 13px;
                    color: {MaterialColors.ON_SURFACE};
                    spacing: 8px;
                    padding: 4px;
                }}
                QRadioButton::indicator {{
                    width: 16px;
                    height: 16px;
                    border-radius: 8px;
                    border: 2px solid {MaterialColors.ON_SURFACE_VARIANT};
                    background-color: transparent;
                }}
                QRadioButton::indicator:checked {{
                    background-color: {MaterialColors.PRIMARY};
                    border: 2px solid {MaterialColors.PRIMARY};
                }}
                QRadioButton::indicator:hover {{
                    border: 2px solid {MaterialColors.PRIMARY};
                }}
            """)

            if style == "American":
                radio.setChecked(True)
                self.selected_style = style

            # Connect with proper lambda capture
            radio.toggled.connect(lambda checked, s=style: self.on_style_changed(s, checked) if checked else None)
            self.style_group.addButton(radio)
            style_layout.addWidget(radio)

        style_group.setLayout(style_layout)
        controls_layout.addWidget(style_group)

        # Generation section
        gen_group = QGroupBox("Generation")
        gen_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: 500;
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 8px;
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.PRIMARY};
            }}
        """)

        gen_layout = QVBoxLayout()
        gen_layout.setSpacing(12)

        # Generate button - single definition
        self.generate_button = MaterialButton("Generate IRs", "filled")
        self.generate_button.setMinimumHeight(48)
        self.generate_button.clicked.connect(self.generate_irs)
        gen_layout.addWidget(self.generate_button)

        # Progress bar
        self.progress_bar = MaterialProgressBar()
        self.progress_bar.setVisible(False)
        gen_layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready to generate IRs")
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            margin: 0;
            padding: 4px 0;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setWordWrap(True)
        gen_layout.addWidget(self.status_label)

        gen_group.setLayout(gen_layout)
        controls_layout.addWidget(gen_group)

        controls_layout.addStretch()
        controls_card.setLayout(controls_layout)
        return controls_card

    def create_functional_generated_irs_panel(self):
        """Create the main Generated IRs panel with prominent display and working export buttons."""
        main_panel = MaterialCard(elevation=2)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(32, 32, 32, 32)
        main_layout.setSpacing(24)

        # Prominent header
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)

        title_label = QLabel("Generated IRs")
        title_label.setStyleSheet(f"""
            font-size: 32px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            margin: 0;
            padding: 0;
        """)

        self.ir_status_label = QLabel("Ready to generate IRs")
        self.ir_status_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            background-color: {MaterialColors.SURFACE_VARIANT};
            border-radius: 16px;
            padding: 8px 16px;
            margin: 0;
        """)
        self.ir_status_label.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.ir_status_label)
        main_layout.addLayout(header_layout)

        # IR list area
        self.ir_list_widget = QWidget()
        self.ir_list_widget.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE};
            border: 2px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
        """)

        self.ir_list_layout = QVBoxLayout()
        self.ir_list_layout.setContentsMargins(16, 16, 16, 16)
        self.ir_list_layout.setSpacing(12)
        self.ir_list_widget.setLayout(self.ir_list_layout)

        # Add placeholder
        placeholder = QLabel("No IRs generated yet.\nClick 'Generate IRs' to create impulse responses.")
        placeholder.setStyleSheet(f"""
            font-size: 16px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            padding: 40px;
            border: 2px dashed {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
            background-color: {MaterialColors.SURFACE_VARIANT};
        """)
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setWordWrap(True)
        self.ir_list_layout.addWidget(placeholder)

        # Scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.ir_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(300)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
        """)
        main_layout.addWidget(scroll_area, 1)

        # Export section with working buttons
        export_card = MaterialCard(elevation=1)
        export_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
            border: 2px solid {MaterialColors.PRIMARY};
        """)

        export_layout = QVBoxLayout()
        export_layout.setContentsMargins(24, 20, 24, 20)
        export_layout.setSpacing(16)

        export_title = QLabel("Export Options")
        export_title.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 600;
            color: {MaterialColors.PRIMARY};
            margin: 0;
            padding: 0 0 8px 0;
        """)
        export_title.setAlignment(Qt.AlignCenter)
        export_layout.addWidget(export_title)

        # Export buttons - single definitions with proper connections
        button_layout = QHBoxLayout()
        button_layout.setSpacing(16)

        self.export_individual_button = MaterialButton("📁 Export Individual IRs", "filled")
        self.export_individual_button.setMinimumHeight(56)
        self.export_individual_button.clicked.connect(self.export_individual_irs)
        self.export_individual_button.setEnabled(False)

        self.export_combined_button = MaterialButton("🔗 Export Combined IR", "outlined")
        self.export_combined_button.setMinimumHeight(56)
        self.export_combined_button.clicked.connect(self.export_combined_ir)
        self.export_combined_button.setEnabled(False)

        button_layout.addWidget(self.export_individual_button)
        button_layout.addWidget(self.export_combined_button)
        export_layout.addLayout(button_layout)

        # Preview button
        self.play_button = MaterialButton("▶ Preview Selected IR", "text")
        self.play_button.setMinimumHeight(48)
        self.play_button.clicked.connect(self.preview_selected_ir)
        self.play_button.setEnabled(False)
        export_layout.addWidget(self.play_button)

        export_card.setLayout(export_layout)
        main_layout.addWidget(export_card)

        main_panel.setLayout(main_layout)
        return main_panel

    def create_functional_visualization_panel(self):
        """Create the visualization panel."""
        viz_card = MaterialCard(elevation=1)
        viz_layout = QVBoxLayout()
        viz_layout.setContentsMargins(24, 24, 24, 24)
        viz_layout.setSpacing(18)

        # Title
        title_label = QLabel("IR Analysis & Visualization")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            margin: 0;
            padding: 0 0 8px 0;
        """)
        viz_layout.addWidget(title_label)

        # Visualization canvas
        self.detail_canvas = DetailCanvas()
        self.detail_canvas.setStyleSheet(f"""
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
            background-color: {MaterialColors.SURFACE};
        """)
        self.detail_canvas.setMinimumHeight(400)
        viz_layout.addWidget(self.detail_canvas)

        viz_card.setLayout(viz_layout)
        return viz_card











    def create_generated_irs_panel(self):
        """Create the main Generated IRs panel as the central focus of the application."""
        main_panel = MaterialCard(elevation=2)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(32, 32, 32, 32)
        main_layout.setSpacing(24)

        # Prominent title section
        title_section = QWidget()
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(20)

        # Large, prominent title
        title_label = QLabel("Generated IRs")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            border: none;
            margin: 0;
            padding: 0;
            line-height: 1.2;
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # Status indicator
        self.ir_status_label = QLabel("Ready to generate IRs")
        self.ir_status_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 8px 16px;
            background-color: {MaterialColors.SURFACE_VARIANT};
            border-radius: 16px;
        """)
        self.ir_status_label.setAlignment(Qt.AlignCenter)

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.ir_status_label)
        title_section.setLayout(title_layout)
        main_layout.addWidget(title_section)

        # IR list with enhanced visibility
        self.ir_list_widget = QWidget()
        self.ir_list_widget.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE};
            border: 2px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
            padding: 16px;
        """)
        self.ir_list_layout = QVBoxLayout()
        self.ir_list_layout.setContentsMargins(16, 16, 16, 16)
        self.ir_list_layout.setSpacing(12)
        self.ir_list_widget.setLayout(self.ir_list_layout)

        # Scroll area for IR list
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.ir_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(300)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {MaterialColors.PRIMARY};
                border-radius: 6px;
                min-height: 20px;
            }}
        """)
        main_layout.addWidget(scroll_area, 1)

        # Prominent export controls section
        export_section = self.create_prominent_export_section()
        main_layout.addWidget(export_section)

        main_panel.setLayout(main_layout)
        main_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        return main_panel

    def create_prominent_export_section(self):
        """Create a prominent export section with large, visible buttons."""
        export_card = MaterialCard(elevation=1)
        export_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
            border: 2px solid {MaterialColors.PRIMARY};
        """)

        export_layout = QVBoxLayout()
        export_layout.setContentsMargins(24, 20, 24, 20)
        export_layout.setSpacing(16)

        # Export section title
        export_title = QLabel("Export Options")
        export_title.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 600;
            color: {MaterialColors.PRIMARY};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
        """)
        export_title.setAlignment(Qt.AlignCenter)
        export_layout.addWidget(export_title)

        # Button container for side-by-side layout
        button_container = QWidget()
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(16)

        # Large, prominent export buttons
        self.export_individual_button = MaterialButton("📁 Export Individual IRs", "filled")
        self.export_individual_button.setMinimumHeight(56)
        self.export_individual_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 28px;
                padding: 16px 32px;
                font-weight: 600;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.PRIMARY_VARIANT};
            }}
            QPushButton:disabled {{
                background-color: {MaterialColors.DISABLED};
                color: {MaterialColors.ON_SURFACE_VARIANT};
            }}
        """)
        self.export_individual_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_individual_button.clicked.connect(self.export_individual_irs)
        self.export_individual_button.setEnabled(False)

        self.export_combined_button = MaterialButton("🔗 Export Combined IR", "outlined")
        self.export_combined_button.setMinimumHeight(56)
        self.export_combined_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {MaterialColors.PRIMARY};
                border: 3px solid {MaterialColors.PRIMARY};
                border-radius: 28px;
                padding: 16px 32px;
                font-weight: 600;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.HOVER};
                border-color: {MaterialColors.PRIMARY_VARIANT};
            }}
            QPushButton:disabled {{
                color: {MaterialColors.DISABLED};
                border-color: {MaterialColors.DISABLED};
            }}
        """)
        self.export_combined_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_combined_button.clicked.connect(self.export_combined_ir)
        self.export_combined_button.setEnabled(False)

        button_layout.addWidget(self.export_individual_button)
        button_layout.addWidget(self.export_combined_button)
        button_container.setLayout(button_layout)
        export_layout.addWidget(button_container)

        # Preview button
        self.play_button = MaterialButton("▶ Preview Selected IR", "text")
        self.play_button.setMinimumHeight(48)
        self.play_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                color: {MaterialColors.PRIMARY};
                border: none;
                border-radius: 24px;
                padding: 12px 24px;
                font-weight: 500;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
            }}
            QPushButton:disabled {{
                color: {MaterialColors.DISABLED};
                background-color: {MaterialColors.SURFACE_VARIANT};
            }}
        """)
        self.play_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.play_button.clicked.connect(self.preview_selected_ir)
        self.play_button.setEnabled(False)
        export_layout.addWidget(self.play_button)

        export_card.setLayout(export_layout)
        return export_card

    def create_right_panel(self):
        """Create the right panel with IR list and visualization."""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(24)
        right_layout.setAlignment(Qt.AlignTop)

        # IR List section
        ir_list_card = self.create_ir_list_card()
        ir_list_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        right_layout.addWidget(ir_list_card)

        # Visualization section
        viz_card = self.create_visualization_card()
        viz_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        right_layout.addWidget(viz_card, 1)

        right_widget.setLayout(right_layout)
        right_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        return right_widget

    def create_visualization_panel(self):
        """Create the visualization panel as a separate column."""
        viz_card = MaterialCard(elevation=1)
        viz_layout = QVBoxLayout()
        viz_layout.setContentsMargins(24, 24, 24, 24)
        viz_layout.setSpacing(18)

        # Title with clean styling
        title_label = QLabel("IR Analysis & Visualization")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
        """)
        title_label.setAlignment(Qt.AlignLeft)
        viz_layout.addWidget(title_label)

        # Detail canvas with clean borders
        self.detail_canvas = DetailCanvas()
        self.detail_canvas.setStyleSheet(f"""
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
            background-color: {MaterialColors.SURFACE};
        """)
        self.detail_canvas.setMinimumHeight(400)
        viz_layout.addWidget(self.detail_canvas)

        viz_card.setLayout(viz_layout)
        viz_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        return viz_card





    def create_status_bar(self):
        """Create the modern status bar with clean styling."""
        status_bar = QStatusBar()
        status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE_VARIANT};
                border-top: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-left: none;
                border-right: none;
                border-bottom: none;
                font-size: 12px;
                padding: 8px 16px;
                margin: 0;
            }}
            QStatusBar::item {{
                border: none;
                margin: 0;
                padding: 0;
            }}
        """)
        status_bar.showMessage("Ready - Select characteristics and generate IRs")
        self.setStatusBar(status_bar)

    def initialize_exporter(self):
        """Initialize the IR exporter."""
        try:
            self.exporter = IRExporter()
            self.statusBar().showMessage(f"Ready - Loaded {len(self.exporter.ir_reader.irs)} IRs from dataset")
        except Exception as e:
            self.show_message("Error", f"Failed to initialize IR exporter: {str(e)}")

    def on_style_changed(self, style, checked):
        """Handle style selection change."""
        if checked:
            self.selected_style = style
            self.status_label.setText(f"Style: {style} - Ready to generate")

    def generate_irs(self):
        """Generate IRs using the modern interface."""
        # Check if generation is already in progress
        if hasattr(self, 'worker_thread') and self.worker_thread and not self.worker_thread.isFinished():
            return

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.generate_button.setEnabled(False)
        self.status_label.setText("Generating IRs...")

        # Create worker thread
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(self.exporter, self.selected_style, 10)
        self.worker.moveToThread(self.worker_thread)

        # Connect signals
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)

        # Clean up thread when finished
        def cleanup_thread():
            if hasattr(self, 'worker_thread') and self.worker_thread:
                self.worker_thread.deleteLater()
                self.worker_thread = None

        self.worker_thread.finished.connect(cleanup_thread)

        # Start generation
        self.worker_thread.start()

    def closeEvent(self, event):
        """Handle application close event."""
        # Clean up worker thread if running
        if hasattr(self, 'worker_thread') and self.worker_thread and not self.worker_thread.isFinished():
            self.worker_thread.quit()
            self.worker_thread.wait(3000)  # Wait up to 3 seconds

        # Clean up temporary files
        if hasattr(self, 'current_temp_file') and self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except:
                pass

        event.accept()

    def add_ir_placeholder(self):
        """Add placeholder when no IRs are generated."""
        placeholder = QLabel("No IRs generated yet.\nClick 'Generate All IRs' to create impulse responses.")
        placeholder.setStyleSheet(f"""
            font-size: 16px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            padding: 40px;
            border: 2px dashed {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
            background-color: {MaterialColors.SURFACE_VARIANT};
        """)
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setWordWrap(True)
        self.ir_list_layout.addWidget(placeholder)

    def select_all_irs(self):
        """Select all generated IRs."""
        if self.last_generated_irs:
            self.selected_ir_indices = set(range(len(self.last_generated_irs)))
            self.update_ir_list_display()
            self.update_selection_info()

    def clear_ir_selection(self):
        """Clear all IR selections."""
        self.selected_ir_indices.clear()
        self.update_ir_list_display()
        self.update_selection_info()

    def toggle_ir_selection(self, index):
        """Toggle selection of specific IR."""
        if index in self.selected_ir_indices:
            self.selected_ir_indices.remove(index)
        else:
            self.selected_ir_indices.add(index)
        self.update_selection_info()

    def update_selection_info(self):
        """Update selection information display."""
        if not self.last_generated_irs:
            self.selection_info_label.setText("No IRs generated")
            self.selection_info_label.setStyleSheet(f"""
                font-size: 14px;
                color: {MaterialColors.ON_SURFACE_VARIANT};
                background-color: {MaterialColors.SURFACE_VARIANT};
                border-radius: 12px;
                padding: 6px 12px;
                margin: 0;
            """)
        else:
            total = len(self.last_generated_irs)
            selected = len(self.selected_ir_indices)

            if selected == 0:
                text = f"{total} IRs • None selected"
                color = MaterialColors.ON_SURFACE_VARIANT
                bg_color = MaterialColors.SURFACE_VARIANT
            else:
                text = f"{selected}/{total} IRs selected"
                color = MaterialColors.ON_PRIMARY
                bg_color = MaterialColors.PRIMARY

            self.selection_info_label.setText(text)
            self.selection_info_label.setStyleSheet(f"""
                font-size: 14px;
                color: {color};
                background-color: {bg_color};
                border-radius: 12px;
                padding: 6px 12px;
                margin: 0;
            """)

        # Update button states
        has_selection = len(self.selected_ir_indices) > 0
        self.export_selected_button.setEnabled(has_selection)
        self.export_combined_button.setEnabled(has_selection)
        self.play_button.setEnabled(has_selection)
        self.clear_selection_button.setEnabled(has_selection)

    def on_generation_finished(self, irs):
        """Handle generation completion."""
        self.last_generated_irs = irs
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.export_individual_button.setEnabled(True)
        self.export_combined_button.setEnabled(True)
        self.play_button.setEnabled(True)

        self.status_label.setText(f"Generated {len(irs)} IRs successfully")
        self.statusBar().showMessage(f"Generated {len(irs)} IRs - Ready for preview and export")

        # Update IR list display
        self.update_ir_list_display()

        # Select first IR by default
        if irs:
            self.select_ir(0)

    def on_generation_error(self, error_msg):
        """Handle generation error."""
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.status_label.setText("Generation failed")
        self.show_message("Error", f"IR generation failed: {error_msg}")

    def update_ir_list_display(self):
        """Update the IR list display with multi-selection support."""
        # Clear existing widgets
        for i in reversed(range(self.ir_list_layout.count())):
            child = self.ir_list_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.last_generated_irs:
            self.add_ir_placeholder()
            # Disable controls
            self.select_all_button.setEnabled(False)
            self.clear_selection_button.setEnabled(False)
            self.update_selection_info()
            return

        # Enable controls
        self.select_all_button.setEnabled(True)

        # Create IR cards with multi-selection
        for i, ir in enumerate(self.last_generated_irs):
            ir_card = self.create_multiselect_ir_card(i, ir)
            self.ir_list_layout.addWidget(ir_card)

        # Update selection info
        self.update_selection_info()

    def create_multiselect_ir_card(self, index, ir):
        """Create IR card with multi-selection support."""
        is_selected = index in self.selected_ir_indices

        item_card = MaterialCard(elevation=1)
        item_card.setStyleSheet(f"""
            background-color: {MaterialColors.PRIMARY if is_selected else MaterialColors.SURFACE_CONTAINER};
            border: 2px solid {MaterialColors.PRIMARY if is_selected else MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
        """)
        item_card.setMinimumHeight(80)
        item_card.setMaximumHeight(80)

        layout = QHBoxLayout()
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(16)

        # Selection checkbox
        checkbox = QCheckBox()
        checkbox.setChecked(is_selected)
        checkbox.setStyleSheet(f"""
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 4px;
                border: 2px solid {MaterialColors.ON_SURFACE_VARIANT if not is_selected else MaterialColors.ON_PRIMARY};
                background-color: {'transparent' if not is_selected else MaterialColors.ON_PRIMARY};
            }}
            QCheckBox::indicator:checked {{
                background-color: {MaterialColors.PRIMARY if not is_selected else MaterialColors.ON_PRIMARY};
                border: 2px solid {MaterialColors.PRIMARY if not is_selected else MaterialColors.ON_PRIMARY};
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }}
        """)
        checkbox.toggled.connect(lambda checked: self.toggle_ir_selection(index))
        layout.addWidget(checkbox)

        # IR number
        ir_number = QLabel(f"#{index + 1}")
        ir_number.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 700;
            color: {MaterialColors.ON_PRIMARY if is_selected else MaterialColors.PRIMARY};
            background-color: {MaterialColors.ON_PRIMARY + '20' if is_selected else MaterialColors.PRIMARY + '20'};
            border-radius: 6px;
            padding: 6px 10px;
            min-width: 35px;
        """)
        ir_number.setAlignment(Qt.AlignCenter)
        layout.addWidget(ir_number)

        # IR info
        info_widget = QWidget()
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(2)

        ir_title = QLabel(f"Impulse Response {index + 1}")
        ir_title.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 600;
            color: {MaterialColors.ON_PRIMARY if is_selected else MaterialColors.ON_SURFACE};
            margin: 0;
            padding: 0;
        """)

        # Calculate stats
        rms = np.sqrt(np.mean(ir**2))
        peak = np.max(np.abs(ir))
        duration = len(ir) / 48000  # Assuming 48kHz

        stats_label = QLabel(f"Duration: {duration:.3f}s • RMS: {rms:.4f} • Peak: {peak:.4f}")
        stats_label.setStyleSheet(f"""
            font-size: 11px;
            color: {MaterialColors.ON_PRIMARY + 'CC' if is_selected else MaterialColors.ON_SURFACE_VARIANT};
            margin: 0;
            padding: 0;
        """)

        info_layout.addWidget(ir_title)
        info_layout.addWidget(stats_label)
        info_widget.setLayout(info_layout)
        layout.addWidget(info_widget, 1)

        # Preview button
        preview_btn = MaterialButton("▶", "outlined")
        preview_btn.setMinimumHeight(32)
        preview_btn.setMaximumWidth(32)
        preview_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {MaterialColors.ON_PRIMARY if is_selected else MaterialColors.PRIMARY};
                border: 1px solid {MaterialColors.ON_PRIMARY if is_selected else MaterialColors.PRIMARY};
                border-radius: 16px;
                font-size: 12px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.ON_PRIMARY + '20' if is_selected else MaterialColors.PRIMARY + '20'};
            }}
        """)
        preview_btn.clicked.connect(lambda: self.preview_specific_ir(index))
        layout.addWidget(preview_btn)

        item_card.setLayout(layout)

        # Make the whole card clickable for selection
        def card_clicked():
            checkbox.setChecked(not checkbox.isChecked())

        item_card.mousePressEvent = lambda event: card_clicked() if event.button() == Qt.LeftButton else None

        return item_card

    def export_selected_irs(self):
        """Export only selected IRs."""
        if not self.selected_ir_indices or not self.last_generated_irs:
            self.show_message("Warning", "No IRs selected for export.")
            return

        folder = QFileDialog.getExistingDirectory(self, "Select Export Directory")
        if not folder:
            return

        try:
            selected_irs = [self.last_generated_irs[i] for i in sorted(self.selected_ir_indices)]
            self.exporter.export_individual_irs(selected_irs, folder, prefix="selected_ir")
            self.show_message("Success", f"Successfully exported {len(selected_irs)} selected IRs to {folder}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def preview_selected_irs(self):
        """Preview the first selected IR."""
        if not self.selected_ir_indices or not self.last_generated_irs:
            self.show_message("Warning", "No IRs selected for preview.")
            return

        # Preview the first selected IR
        first_selected = min(self.selected_ir_indices)
        self.preview_specific_ir(first_selected)

    def create_functional_ir_item_card(self, index, ir):
        """Create a functional IR item card with working buttons."""
        item_card = MaterialCard(elevation=1)
        item_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER};
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
        """)
        item_card.setMinimumHeight(70)

        layout = QHBoxLayout()
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(16)

        # IR number
        ir_number = QLabel(f"#{index + 1}")
        ir_number.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            background-color: {MaterialColors.PRIMARY}20;
            border-radius: 6px;
            padding: 6px 10px;
            min-width: 35px;
        """)
        ir_number.setAlignment(Qt.AlignCenter)
        layout.addWidget(ir_number)

        # IR info
        info_widget = QWidget()
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(2)

        ir_title = QLabel(f"Impulse Response {index + 1}")
        ir_title.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            margin: 0;
            padding: 0;
        """)

        # Calculate stats
        rms = np.sqrt(np.mean(ir**2))
        peak = np.max(np.abs(ir))
        duration = len(ir) / 48000  # Assuming 48kHz

        stats_label = QLabel(f"Duration: {duration:.3f}s • RMS: {rms:.4f} • Peak: {peak:.4f}")
        stats_label.setStyleSheet(f"""
            font-size: 11px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            margin: 0;
            padding: 0;
        """)

        info_layout.addWidget(ir_title)
        info_layout.addWidget(stats_label)
        info_widget.setLayout(info_layout)
        layout.addWidget(info_widget, 1)

        # Action buttons
        button_widget = QWidget()
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)

        # Select button
        select_btn = MaterialButton("Select", "filled")
        select_btn.setMinimumHeight(32)
        select_btn.setMaximumWidth(80)
        select_btn.clicked.connect(lambda: self.select_ir(index))

        # Preview button
        preview_btn = MaterialButton("▶", "outlined")
        preview_btn.setMinimumHeight(32)
        preview_btn.setMaximumWidth(32)
        preview_btn.clicked.connect(lambda: self.preview_specific_ir(index))

        button_layout.addWidget(select_btn)
        button_layout.addWidget(preview_btn)
        button_widget.setLayout(button_layout)
        layout.addWidget(button_widget)

        item_card.setLayout(layout)
        return item_card

    def create_enhanced_ir_item_card(self, index, ir):
        """Create an enhanced card for an individual IR item with better visibility."""
        item_card = MaterialCard(elevation=1, interactive=True)
        item_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER};
            border: 2px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
        """)
        item_card.setMinimumHeight(80)

        layout = QHBoxLayout()
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(20)

        # IR number with prominent styling
        ir_number = QLabel(f"#{index + 1}")
        ir_number.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            border: none;
            margin: 0;
            padding: 8px 12px;
            background-color: {MaterialColors.PRIMARY}20;
            border-radius: 8px;
            min-width: 40px;
        """)
        ir_number.setAlignment(Qt.AlignCenter)
        layout.addWidget(ir_number)

        # IR info section
        info_section = QWidget()
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(4)

        # IR title
        ir_title = QLabel(f"Impulse Response {index + 1}")
        ir_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0;
        """)

        # IR stats with better formatting
        rms = np.sqrt(np.mean(ir**2))
        peak = np.max(np.abs(ir))
        duration = len(ir) / 48000  # Assuming 48kHz sample rate

        stats_text = f"Duration: {duration:.3f}s • RMS: {rms:.4f} • Peak: {peak:.4f}"
        stats_label = QLabel(stats_text)
        stats_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 0;
        """)

        info_layout.addWidget(ir_title)
        info_layout.addWidget(stats_label)
        info_section.setLayout(info_layout)
        layout.addWidget(info_section, 1)

        # Action buttons
        button_section = QWidget()
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)

        # Select button
        select_btn = MaterialButton("Select", "filled")
        select_btn.setMinimumHeight(36)
        select_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 18px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.PRIMARY_VARIANT};
            }}
        """)
        select_btn.clicked.connect(lambda: self.select_ir(index))

        # Preview button
        preview_btn = MaterialButton("▶", "outlined")
        preview_btn.setMinimumHeight(36)
        preview_btn.setMaximumWidth(36)
        preview_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {MaterialColors.PRIMARY};
                border: 2px solid {MaterialColors.PRIMARY};
                border-radius: 18px;
                padding: 8px;
                font-weight: 500;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.HOVER};
            }}
        """)
        preview_btn.clicked.connect(lambda: self.preview_specific_ir(index))

        button_layout.addWidget(select_btn)
        button_layout.addWidget(preview_btn)
        button_section.setLayout(button_layout)
        layout.addWidget(button_section)

        item_card.setLayout(layout)
        return item_card

    def select_ir(self, index):
        """Select an IR for preview and visualization."""
        if not self.last_generated_irs or index >= len(self.last_generated_irs):
            return

        ir = self.last_generated_irs[index]
        self.detail_canvas.update_detail(ir, 48000)
        self.selected_ir_index = index
        self.statusBar().showMessage(f"Selected IR {index + 1} - Ready for preview")

    def preview_selected_ir(self):
        """Preview the selected IR."""
        if not hasattr(self, 'selected_ir_index') or not self.last_generated_irs:
            return

        ir = self.last_generated_irs[self.selected_ir_index]
        self._preview_ir(ir)

    def preview_specific_ir(self, index):
        """Preview a specific IR by index."""
        if not self.last_generated_irs or index >= len(self.last_generated_irs):
            return

        # Select the IR first
        self.select_ir(index)
        # Then preview it
        ir = self.last_generated_irs[index]
        self._preview_ir(ir)

    def _preview_ir(self, ir):
        """Enhanced IR preview with stereo audio support."""
        sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
        try:
            # Load sample with stereo detection
            sample_data, sample_rate, channels = stereo_handler.load_audio(sample_path)
            logging.info(f"Loaded sample: {channels} channels, {sample_rate}Hz")
        except Exception as e:
            self.show_message("Error", f"Error loading sample file: {str(e)}")
            return

        try:
            # Perform stereo-aware convolution
            if len(sample_data.shape) > 1:  # Stereo sample
                convolved = stereo_handler.stereo_convolution(sample_data, ir, mode="stereo")
            else:  # Mono sample
                convolved = fftconvolve(sample_data, ir, mode="full")

            # Normalize while preserving stereo balance
            convolved = stereo_handler.normalize_audio(convolved, target_level=0.9)

        except Exception as e:
            self.show_message("Error", f"Error processing audio: {str(e)}")
            return

        # Clean up previous temp file
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
            self.current_temp_file = None

        # Create new temp file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_filename = temp_file.name
        temp_file.close()

        try:
            # Save with stereo format preservation
            stereo_handler.save_audio(convolved, temp_filename, sample_rate)
        except Exception as e:
            self.show_message("Error", f"Error exporting preview file: {str(e)}")
            return

        self.current_temp_file = temp_filename
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(temp_filename)))
        self.player.play()

    def export_individual_irs(self):
        """Export individual IRs."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        folder = QFileDialog.getExistingDirectory(self, "Select Export Folder")
        if not folder:
            return

        try:
            for i, ir in enumerate(self.last_generated_irs):
                filename = self.get_unique_filename(folder, f"IR_{i+1:02d}.wav")
                stereo_handler.save_audio(ir, filename, 48000, 'PCM_24')

            self.show_message("Success", f"Exported {len(self.last_generated_irs)} IRs to {folder}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def export_combined_ir(self):
        """Export combined IR."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        filename, _ = QFileDialog.getSaveFileName(self, "Save Combined IR", "Combined_IR.wav", "WAV files (*.wav)")
        if not filename:
            return

        try:
            # Average all IRs
            combined_ir = np.mean(self.last_generated_irs, axis=0)
            stereo_handler.save_audio(combined_ir, filename, 48000, 'PCM_24')
            self.show_message("Success", f"Combined IR exported to {filename}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    def show_message(self, title, message):
        """Show a message dialog."""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE};
            }}
            QMessageBox QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }}
        """)
        msg_box.exec_()

    def _on_media_status_changed(self, status):
        """Handle media player status changes."""
        if status == QMediaPlayer.EndOfMedia:
            if self.current_temp_file and os.path.exists(self.current_temp_file):
                try:
                    os.remove(self.current_temp_file)
                except Exception:
                    pass
                self.current_temp_file = None

    def closeEvent(self, event):
        """Handle application close."""
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
        event.accept()

if __name__ == "__main__":
    # Enable high-DPI scaling before creating QApplication
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("IR-Alchemist")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Develop Device")

    # Create and show the modern GUI
    window = ModernIRGeneratorGUI()
    window.show()

    sys.exit(app.exec_())
