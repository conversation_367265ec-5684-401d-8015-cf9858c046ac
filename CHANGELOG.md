# Changelog

All notable changes to IR-<PERSON><PERSON> will be documented in this file.

## [2.0.0] - Dataset Version

### Major Changes
- **Replaced machine learning approach with dataset-based processing**
  - Removed PyTorch dependency and neural network model
  - Now loads IRs from a directory of WAV files (`E:\IRs\IR-Alchemist IRs`)
  - Maintains the same post-processing pipeline for IR enhancement

### Added
- `irpkg_reader.py` - New IR dataset reader that loads WAV files from directory
- `test_ir_exporter.py` - Standalone test script for the IR processing pipeline
- `IR-Alchemist-Simple.py` - Simplified version without matplotlib dependency
- `README.md` - Updated documentation for the dataset approach
- `requirements.txt` - Updated dependencies list

### Removed
- `trainingmodel.py` - Neural network model definition
- `ir_model_final.pth` - Pre-trained model weights
- PyTorch dependency
- TensorBoard dependency
- CUDA support code

### Changed
- `IRExporter` class now uses dataset reader instead of ML model
- `IRGenerationWorker` simplified to work with dataset selection
- Matplotlib is now optional (graceful fallback when not available)
- Updated `.spec` file to remove model file from bundle

### Technical Details
- Successfully loads 152 IR files from the dataset directory
- Maintains all post-processing effects (resonators, filters, EQ, etc.)
- Preserves the same user interface and workflow
- Improved error handling and logging
- Better separation of concerns between data loading and processing

### Benefits
- Faster startup time (no model loading)
- Reduced memory usage
- More predictable and consistent results
- Easier to add new IRs (just add WAV files to directory)
- No dependency on GPU/CUDA
- Simplified deployment and distribution
