#!/usr/bin/env python3
"""
Test script for the new dataset-based IR exporter.
This tests the core functionality without the GUI.
"""

import os
import sys
import numpy as np
import soundfile as sf
from scipy.signal import butter, lfilter, fftconvolve
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

# Import our IR reader
from irpkg_reader import IRPKGReader

class IRExporter:
    def __init__(self, ir_dir="E:\\IRs\\IR-Alchemist IRs"):
        # Import the IR reader
        if ir_dir is None:
            # Use packaged dataset
            self.ir_reader = IRPKGReader()
        else:
            # For backward compatibility, we'll still try to use the packaged dataset
            # since the new IRPKGReader only supports packaged format
            self.ir_reader = IRPKGReader()
        self.ir_length = 2048
        self.pp_params = {
            "resonators": {"min_count": 1, "max_count": 4, "gain_range": (0.8, 1.2)},
            "fixed_filters": {"hp_cutoff": 80, "lp_cutoff": 9500, "order": 3},
            "random_eq": {"num_points": 8, "gain_range": (0.5, 1.5)},
            "amp_simulation": {"drive": 1.5, "mid_band": (300, 3000), "mid_gain_range": (0.8, 1.2)},
            "suppress_resonances": {"threshold": 1.5, "window_size": 9},
            "high_shelf": {"cutoff": 4000, "shelf_gain": 1.2},
            "custom_style": {"bass_boost": 1.05, "mid_boost": 1.05, "high_boost": 1.05}
        }

    def apply_resonators(self, ir, sample_rate=48000):
        params = self.pp_params["resonators"]
        min_count = params["min_count"]
        max_count = params["max_count"]
        count = min_count if min_count == max_count else np.random.randint(min_count, max_count + 1)
        processed = np.copy(ir)
        for _ in range(count):
            fc = np.random.uniform(1000, 8000)
            Q = np.random.uniform(10, 30)
            bw = fc / Q
            low = max(fc - bw / 2, 20)
            high = min(fc + bw / 2, sample_rate / 2 - 1)
            low_norm = low / (sample_rate / 2)
            high_norm = high / (sample_rate / 2)
            try:
                b, a = butter(2, [low_norm, high_norm], btype='band')
            except ValueError as ve:
                logging.error("Error creating resonator filters: %s", str(ve))
                continue
            filtered = lfilter(b, a, ir)
            gain = np.random.uniform(*params["gain_range"])
            processed += gain * filtered
        return processed

    def apply_fixed_filters(self, ir, sample_rate=48000):
        params = self.pp_params["fixed_filters"]
        hp_norm = params["hp_cutoff"] / (sample_rate / 2)
        b_hp, a_hp = butter(params["order"], hp_norm, btype='high')
        ir_hp = lfilter(b_hp, a_hp, ir)
        lp_norm = params["lp_cutoff"] / (sample_rate / 2)
        b_lp, a_lp = butter(params["order"], lp_norm, btype='low')
        return lfilter(b_lp, a_lp, ir_hp)

    def apply_random_eq(self, ir, sample_rate=48000):
        params = self.pp_params["random_eq"]
        n = len(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        IR_fft = np.fft.rfft(ir)
        freq_points = np.linspace(80, 9500, num=params["num_points"])
        random_gains = np.random.uniform(*params["gain_range"], size=params["num_points"])
        gain_curve = np.interp(freqs, freq_points, random_gains)
        kernel = np.ones(3) / 3
        smooth_curve = np.convolve(gain_curve, kernel, mode='same')
        blended_curve = 0.5 * gain_curve + 0.5 * smooth_curve
        IR_fft *= blended_curve
        return np.fft.irfft(IR_fft, n=n)

    def apply_amp_simulation(self, ir, sample_rate=48000):
        params = self.pp_params["amp_simulation"]
        drive = params["drive"]
        saturated = np.tanh(drive * ir)
        b_mid, a_mid = butter(2, [params["mid_band"][0] / (sample_rate / 2),
                                   params["mid_band"][1] / (sample_rate / 2)], btype='band')
        mid = lfilter(b_mid, a_mid, saturated)
        mid_gain = np.random.uniform(*params["mid_gain_range"])
        mid *= mid_gain
        return 0.5 * saturated + 0.5 * mid

    def suppress_unpleasant_resonances(self, ir, sample_rate=48000):
        params = self.pp_params["suppress_resonances"]
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        mag = np.abs(IR_fft)
        phase = np.angle(IR_fft)
        kernel = np.ones(params["window_size"]) / params["window_size"]
        mag_smoothed = np.convolve(mag, kernel, mode='same')
        mag_new = np.minimum(mag, params["threshold"] * mag_smoothed)
        new_fft = mag_new * np.exp(1j * phase)
        return np.fft.irfft(new_fft, n=n)

    def apply_style(self, ir, style, sample_rate=48000):
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        boost = np.ones_like(IR_fft)
        if style == "German":
            boost[(freqs >= 4000) & (freqs <= 6000)] *= 1.10
        elif style == "British":
            boost[(freqs >= 300) & (freqs <= 800)] *= 1.15
        elif style == "American":
            boost[freqs < 300] *= 1.10
        elif style == "Random":
            # For "Random", use a random modulation of the boost curve
            boost = np.random.uniform(0.8, 1.2, size=boost.shape)
        new_fft = IR_fft * boost
        return np.fft.irfft(new_fft, n=n)

    def apply_high_shelf(self, ir, sample_rate=48000):
        params = self.pp_params["high_shelf"]
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        IR_fft[freqs >= params["cutoff"]] *= params["shelf_gain"]
        return np.fft.irfft(IR_fft, n=n)

    def process_ir(self, ir, style="American", sample_rate=48000):
        ir = self.apply_resonators(ir, sample_rate)
        ir = self.apply_fixed_filters(ir, sample_rate)
        ir = self.apply_random_eq(ir, sample_rate)
        ir = self.apply_amp_simulation(ir, sample_rate)
        ir = self.suppress_unpleasant_resonances(ir, sample_rate)
        ir = self.apply_style(ir, style, sample_rate)
        ir = self.apply_high_shelf(ir, sample_rate)
        norm_factor = np.max(np.abs(ir)) + 1e-9
        return ir / norm_factor

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        # Get IRs from the dataset based on style
        irs = self.ir_reader.get_irs_by_style(style, count)
        
        # Apply post-processing to each IR
        processed_irs = []
        for ir in irs:
            # Apply random gain variation
            gain = np.random.uniform(0.9, 1.1)
            ir = ir * gain
            
            # Apply post-processing (same as before but without ML model)
            ir = self.process_ir(ir, style=style, sample_rate=48000)
            processed_irs.append(ir)
        
        return processed_irs

def test_ir_exporter():
    """Test the IR exporter functionality."""
    print("Testing IR Exporter...")
    
    try:
        # Create the exporter
        exporter = IRExporter()
        print(f"✓ IRExporter created successfully")
        print(f"✓ Loaded {len(exporter.ir_reader.irs)} IRs from dataset")
        
        # Test each style
        for style in ["American", "British", "German", "Random"]:
            print(f"\nTesting style: {style}")
            irs = exporter.generate_multiple_irs(count=3, style=style)
            print(f"✓ Generated {len(irs)} IRs for {style} style")
            
            for i, ir in enumerate(irs):
                print(f"  IR {i+1}: shape={ir.shape}, min={ir.min():.4f}, max={ir.max():.4f}")
        
        # Test saving an IR
        print(f"\nTesting IR export...")
        test_irs = exporter.generate_multiple_irs(count=1, style="American")
        if test_irs:
            test_filename = "test_ir.wav"
            sf.write(test_filename, test_irs[0], 48000, subtype='PCM_24')
            print(f"✓ Exported test IR to {test_filename}")
            
            # Clean up
            if os.path.exists(test_filename):
                os.remove(test_filename)
                print(f"✓ Cleaned up test file")
        
        print(f"\n🎉 All tests passed! The dataset-based IR system is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ir_exporter()
