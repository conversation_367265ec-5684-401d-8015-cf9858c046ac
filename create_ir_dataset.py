#!/usr/bin/env python3
"""
Create IR Dataset Package Script
This script packages the IR files from the source directory into an encrypted dataset.
"""

import os
import sys
import logging
from ir_dataset_packager import IRDatasetPackager

def main():
    """Main function to create the IR dataset package."""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('dataset_creation.log')
        ]
    )
    
    # Configuration
    source_dir = "E:\\IRs\\IR-Alchemist IRs"
    output_file = "ir_dataset.irpkg"
    
    print("=" * 60)
    print("IR-Alchemist Dataset Packager")
    print("=" * 60)
    print(f"Source directory: {source_dir}")
    print(f"Output file: {output_file}")
    print()
    
    # Check if source directory exists
    if not os.path.exists(source_dir):
        print(f"❌ Error: Source directory not found: {source_dir}")
        print("Please ensure the IR files are in the correct location.")
        return 1
    
    # Check if output file already exists
    if os.path.exists(output_file):
        response = input(f"⚠️  Output file '{output_file}' already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Operation cancelled.")
            return 0
        
        try:
            os.remove(output_file)
            print(f"Removed existing file: {output_file}")
        except Exception as e:
            print(f"❌ Error removing existing file: {e}")
            return 1
    
    # Create the packager
    print("🔧 Initializing dataset packager...")
    packager = IRDatasetPackager()
    
    # Create the package
    print("📦 Creating dataset package...")
    print("This may take a few minutes depending on the number of IR files...")
    print()
    
    success = packager.create_dataset_package(source_dir, output_file)
    
    print()
    if success:
        # Get file size for reporting
        file_size = os.path.getsize(output_file)
        file_size_mb = file_size / (1024 * 1024)
        
        print("✅ Dataset package created successfully!")
        print(f"📁 Package file: {output_file}")
        print(f"📊 File size: {file_size:,} bytes ({file_size_mb:.1f} MB)")
        print()
        print("🔒 The dataset is encrypted and compressed.")
        print("🗂️  Original IR files can now be safely removed if desired.")
        print()
        print("Next steps:")
        print("1. Test the application with the new dataset")
        print("2. Verify all functionality works correctly")
        print("3. Backup the original IR directory before removing it")
        
        return 0
    else:
        print("❌ Failed to create dataset package!")
        print("Check the log file 'dataset_creation.log' for details.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
