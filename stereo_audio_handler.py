#!/usr/bin/env python3
"""
Stereo Audio Handler for IR-Alchemist
Provides comprehensive stereo audio processing capabilities.
"""

import numpy as np
import soundfile as sf
import logging
from typing import Tuple, Union, Optional

class StereoAudioHandler:
    """
    Advanced stereo audio processing handler with support for mono/stereo conversion,
    channel separation, and stereo-aware convolution.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def load_audio(self, file_path: str) -> Tuple[np.ndarray, int, int]:
        """
        Load audio file with automatic stereo/mono detection.
        
        Args:
            file_path (str): Path to audio file
            
        Returns:
            tuple: (audio_data, sample_rate, channels)
                - audio_data: numpy array (samples,) for mono or (samples, channels) for stereo
                - sample_rate: sample rate in Hz
                - channels: number of channels (1 for mono, 2 for stereo)
        """
        try:
            data, sample_rate = sf.read(file_path)
            
            # Determine channel count
            if len(data.shape) == 1:
                channels = 1
                self.logger.info(f"Loaded mono audio: {data.shape[0]} samples at {sample_rate}Hz")
            else:
                channels = data.shape[1]
                self.logger.info(f"Loaded {channels}-channel audio: {data.shape[0]} samples at {sample_rate}Hz")
            
            return data, sample_rate, channels
            
        except Exception as e:
            self.logger.error(f"Error loading audio file {file_path}: {e}")
            raise
    
    def ensure_stereo(self, audio: np.ndarray) -> np.ndarray:
        """
        Convert mono audio to stereo by duplicating the channel.
        
        Args:
            audio (np.ndarray): Input audio (mono or stereo)
            
        Returns:
            np.ndarray: Stereo audio (samples, 2)
        """
        if len(audio.shape) == 1:
            # Mono to stereo: duplicate channel
            return np.column_stack([audio, audio])
        elif audio.shape[1] == 1:
            # Single channel to stereo
            return np.column_stack([audio[:, 0], audio[:, 0]])
        else:
            # Already stereo or multi-channel
            return audio[:, :2]  # Take only first two channels
    
    def ensure_mono(self, audio: np.ndarray) -> np.ndarray:
        """
        Convert stereo audio to mono by averaging channels.
        
        Args:
            audio (np.ndarray): Input audio (mono or stereo)
            
        Returns:
            np.ndarray: Mono audio (samples,)
        """
        if len(audio.shape) == 1:
            # Already mono
            return audio
        else:
            # Average all channels to mono
            return np.mean(audio, axis=1)
    
    def separate_channels(self, stereo_audio: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Separate stereo audio into left and right channels.
        
        Args:
            stereo_audio (np.ndarray): Stereo audio (samples, 2)
            
        Returns:
            tuple: (left_channel, right_channel)
        """
        if len(stereo_audio.shape) == 1:
            # Mono audio - return same for both channels
            return stereo_audio, stereo_audio
        else:
            left = stereo_audio[:, 0]
            right = stereo_audio[:, 1] if stereo_audio.shape[1] > 1 else stereo_audio[:, 0]
            return left, right
    
    def combine_channels(self, left: np.ndarray, right: np.ndarray) -> np.ndarray:
        """
        Combine left and right channels into stereo audio.
        
        Args:
            left (np.ndarray): Left channel audio
            right (np.ndarray): Right channel audio
            
        Returns:
            np.ndarray: Stereo audio (samples, 2)
        """
        # Ensure both channels have the same length
        min_length = min(len(left), len(right))
        left = left[:min_length]
        right = right[:min_length]
        
        return np.column_stack([left, right])
    
    def stereo_convolution(self, audio: np.ndarray, ir: np.ndarray, 
                          mode: str = "stereo") -> np.ndarray:
        """
        Perform convolution with stereo awareness.
        
        Args:
            audio (np.ndarray): Input audio (mono or stereo)
            ir (np.ndarray): Impulse response (mono or stereo)
            mode (str): Convolution mode:
                - "stereo": Full stereo processing
                - "mono": Convert to mono, process, then to stereo
                - "left_only": Process left channel only
                - "right_only": Process right channel only
                
        Returns:
            np.ndarray: Processed audio
        """
        from scipy.signal import fftconvolve
        
        if mode == "mono":
            # Convert everything to mono, process, then back to stereo
            audio_mono = self.ensure_mono(audio)
            ir_mono = self.ensure_mono(ir) if len(ir.shape) > 1 else ir
            
            convolved = fftconvolve(audio_mono, ir_mono, mode="full")
            return self.ensure_stereo(convolved)
        
        elif mode in ["left_only", "right_only"]:
            # Process only one channel
            audio_stereo = self.ensure_stereo(audio)
            ir_mono = self.ensure_mono(ir) if len(ir.shape) > 1 else ir
            
            left, right = self.separate_channels(audio_stereo)
            
            if mode == "left_only":
                left_processed = fftconvolve(left, ir_mono, mode="full")
                return self.combine_channels(left_processed, right)
            else:  # right_only
                right_processed = fftconvolve(right, ir_mono, mode="full")
                return self.combine_channels(left, right_processed)
        
        else:  # "stereo" mode
            # Full stereo processing
            audio_stereo = self.ensure_stereo(audio)
            
            if len(ir.shape) == 1:
                # Mono IR - apply to both channels
                left, right = self.separate_channels(audio_stereo)
                left_conv = fftconvolve(left, ir, mode="full")
                right_conv = fftconvolve(right, ir, mode="full")
                return self.combine_channels(left_conv, right_conv)
            else:
                # Stereo IR - apply left IR to left channel, right IR to right channel
                ir_stereo = self.ensure_stereo(ir)
                left_audio, right_audio = self.separate_channels(audio_stereo)
                left_ir, right_ir = self.separate_channels(ir_stereo)
                
                left_conv = fftconvolve(left_audio, left_ir, mode="full")
                right_conv = fftconvolve(right_audio, right_ir, mode="full")
                return self.combine_channels(left_conv, right_conv)
    
    def save_audio(self, audio: np.ndarray, file_path: str, sample_rate: int = 48000,
                   subtype: str = 'PCM_24') -> None:
        """
        Save audio with proper stereo/mono format preservation.
        
        Args:
            audio (np.ndarray): Audio data to save
            file_path (str): Output file path
            sample_rate (int): Sample rate in Hz
            subtype (str): Audio format subtype
        """
        try:
            sf.write(file_path, audio, sample_rate, subtype=subtype)
            
            channels = 1 if len(audio.shape) == 1 else audio.shape[1]
            self.logger.info(f"Saved {channels}-channel audio to {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error saving audio to {file_path}: {e}")
            raise
    
    def get_audio_info(self, audio: np.ndarray, sample_rate: int = 48000) -> dict:
        """
        Get comprehensive information about audio data.
        
        Args:
            audio (np.ndarray): Audio data
            sample_rate (int): Sample rate in Hz
            
        Returns:
            dict: Audio information
        """
        info = {
            'shape': audio.shape,
            'channels': 1 if len(audio.shape) == 1 else audio.shape[1],
            'samples': audio.shape[0],
            'duration': audio.shape[0] / sample_rate,
            'sample_rate': sample_rate,
            'dtype': audio.dtype,
            'min_value': np.min(audio),
            'max_value': np.max(audio),
            'rms': np.sqrt(np.mean(audio**2))
        }
        
        if len(audio.shape) > 1 and audio.shape[1] >= 2:
            left, right = self.separate_channels(audio)
            info['left_rms'] = np.sqrt(np.mean(left**2))
            info['right_rms'] = np.sqrt(np.mean(right**2))
            info['stereo_correlation'] = np.corrcoef(left, right)[0, 1]
        
        return info
    
    def normalize_audio(self, audio: np.ndarray, target_level: float = 0.9) -> np.ndarray:
        """
        Normalize audio to target level while preserving stereo balance.
        
        Args:
            audio (np.ndarray): Input audio
            target_level (float): Target peak level (0.0 to 1.0)
            
        Returns:
            np.ndarray: Normalized audio
        """
        peak = np.max(np.abs(audio))
        if peak > 0:
            gain = target_level / peak
            return audio * gain
        return audio

# Global instance for easy access
stereo_handler = StereoAudioHandler()
