#!/usr/bin/env python3
"""
Comprehensive test for text positioning improvements in native visualization system.
"""

import sys
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer
from native_visualization import NativeDetailCanvas

class TextPositioningTestWindow(QMainWindow):
    """Test window for validating text positioning improvements."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Text Positioning Validation Test")
        self.setGeometry(100, 100, 1400, 900)
        
        # Create central widget
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # Create info label
        info_label = QLabel("Text Positioning Test - Resize window to test dynamic scaling")
        info_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold; padding: 10px;")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # Create test buttons
        button_layout = QHBoxLayout()
        
        self.test_buttons = []
        test_cases = [
            ("Short IR", self.test_short_ir),
            ("Long IR", self.test_long_ir),
            ("High Freq", self.test_high_frequency),
            ("Low Freq", self.test_low_frequency),
            ("Wide Range", self.test_wide_range),
            ("Narrow Range", self.test_narrow_range),
            ("Real IR", self.test_real_ir),
            ("Auto Test", self.auto_test_cycle)
        ]
        
        for name, func in test_cases:
            btn = QPushButton(name)
            btn.clicked.connect(func)
            btn.setStyleSheet("padding: 8px; margin: 2px;")
            button_layout.addWidget(btn)
            self.test_buttons.append(btn)
        
        layout.addLayout(button_layout)
        
        # Create the native detail canvas
        self.detail_canvas = NativeDetailCanvas()
        layout.addWidget(self.detail_canvas)
        
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
        
        # Auto-test timer
        self.auto_timer = QTimer()
        self.auto_timer.timeout.connect(self.next_auto_test)
        self.auto_test_index = 0
        
        # Start with a test case
        self.test_short_ir()
    
    def test_short_ir(self):
        """Test with a short IR (edge case for time labels)."""
        print("Testing short IR...")
        sample_rate = 48000
        length = 512  # Very short IR
        
        # Create short impulse with decay
        ir = np.zeros(length)
        ir[0] = 1.0
        decay = np.exp(-np.arange(length) / (length / 5))
        ir = ir * decay
        
        # Add some frequency content
        t = np.arange(length) / sample_rate
        ir += 0.3 * np.sin(2 * np.pi * 1000 * t) * decay
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_long_ir(self):
        """Test with a long IR (edge case for time labels)."""
        print("Testing long IR...")
        sample_rate = 48000
        length = 8192  # Long IR
        
        # Create long reverb-like IR
        t = np.arange(length) / sample_rate
        ir = np.random.normal(0, 0.1, length)
        
        # Apply exponential decay
        decay = np.exp(-t * 3)
        ir = ir * decay
        
        # Add early reflections
        for delay in [0.01, 0.02, 0.035, 0.05]:
            delay_samples = int(delay * sample_rate)
            if delay_samples < length:
                ir[delay_samples] += 0.5 * np.exp(-delay * 10)
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_high_frequency(self):
        """Test with high-frequency content (edge case for freq labels)."""
        print("Testing high frequency content...")
        sample_rate = 48000
        length = 2048
        
        t = np.arange(length) / sample_rate
        # High frequency sine waves
        ir = 0.5 * np.sin(2 * np.pi * 8000 * t) * np.exp(-t * 20)
        ir += 0.3 * np.sin(2 * np.pi * 12000 * t) * np.exp(-t * 25)
        ir += 0.2 * np.sin(2 * np.pi * 16000 * t) * np.exp(-t * 30)
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_low_frequency(self):
        """Test with low-frequency content (edge case for freq labels)."""
        print("Testing low frequency content...")
        sample_rate = 48000
        length = 2048
        
        t = np.arange(length) / sample_rate
        # Low frequency content
        ir = 0.8 * np.sin(2 * np.pi * 50 * t) * np.exp(-t * 5)
        ir += 0.6 * np.sin(2 * np.pi * 120 * t) * np.exp(-t * 8)
        ir += 0.4 * np.sin(2 * np.pi * 200 * t) * np.exp(-t * 10)
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_wide_range(self):
        """Test with wide dynamic range (edge case for amplitude labels)."""
        print("Testing wide dynamic range...")
        sample_rate = 48000
        length = 2048
        
        t = np.arange(length) / sample_rate
        # Create IR with wide dynamic range
        ir = np.sin(2 * np.pi * 1000 * t) * np.exp(-t * 10)
        
        # Add very quiet tail
        ir[length//2:] *= 0.001
        
        # Add loud initial spike
        ir[0] = 2.0
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_narrow_range(self):
        """Test with narrow dynamic range (edge case for amplitude labels)."""
        print("Testing narrow dynamic range...")
        sample_rate = 48000
        length = 2048
        
        # Create IR with very narrow dynamic range
        ir = np.random.normal(0, 0.01, length)
        ir = ir * np.exp(-np.arange(length) / (length / 3))
        
        # Normalize to small range
        ir = ir / np.max(np.abs(ir)) * 0.1
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_real_ir(self):
        """Test with real IR from dataset."""
        print("Testing real IR from dataset...")
        try:
            from irpkg_reader import IRPKGReader
            reader = IRPKGReader()
            
            if reader.irs:
                # Use a random IR from the dataset
                import random
                ir = random.choice(reader.irs)
                self.detail_canvas.update_detail(ir, 48000)
                print(f"Loaded real IR with {len(ir)} samples")
            else:
                print("No IRs available, using fallback")
                self.test_short_ir()
                
        except Exception as e:
            print(f"Error loading real IR: {e}")
            self.test_short_ir()
    
    def auto_test_cycle(self):
        """Start automatic testing cycle."""
        print("Starting automatic test cycle...")
        self.auto_test_index = 0
        self.auto_timer.start(3000)  # Change test every 3 seconds
    
    def next_auto_test(self):
        """Move to next test in auto cycle."""
        test_functions = [
            self.test_short_ir,
            self.test_long_ir,
            self.test_high_frequency,
            self.test_low_frequency,
            self.test_wide_range,
            self.test_narrow_range,
            self.test_real_ir
        ]
        
        if self.auto_test_index < len(test_functions):
            test_functions[self.auto_test_index]()
            self.auto_test_index += 1
        else:
            self.auto_timer.stop()
            print("Auto test cycle completed!")
    
    def resizeEvent(self, event):
        """Handle resize events to test dynamic scaling."""
        super().resizeEvent(event)
        print(f"Window resized to: {self.width()}x{self.height()}")

def main():
    """Main test function."""
    print("Text Positioning Validation Test")
    print("=" * 50)
    print("This test validates the text positioning improvements:")
    print("1. No overlapping labels")
    print("2. Proper spacing between text elements")
    print("3. Dynamic scaling with window size")
    print("4. Collision detection working")
    print("5. Professional appearance maintained")
    print()
    print("Instructions:")
    print("- Use buttons to test different IR types")
    print("- Resize window to test dynamic scaling")
    print("- Check that all text is readable and properly positioned")
    print("- Verify no text overlaps with plot elements")
    print()
    
    app = QApplication(sys.argv)
    
    # Create test window
    test_window = TextPositioningTestWindow()
    test_window.show()
    
    print("✅ Text positioning test window launched!")
    print("Resize the window and test different IR types to validate improvements.")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
