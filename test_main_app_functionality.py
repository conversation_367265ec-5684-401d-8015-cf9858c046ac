#!/usr/bin/env python3
"""
Test script to verify the main IR-Alchemist application functionality.
"""

import os
import sys
import tempfile
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_main_app_components():
    """Test the main application components without GUI."""
    print("Testing Main Application Components")
    print("=" * 50)
    
    try:
        # Test IRExporter from main app
        print("1. Testing IRExporter from main application...")
        
        # Import the main module
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_app", "IR-Alchemist.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        # Create IRExporter
        exporter = main_module.IRExporter()
        print(f"   ✓ IRExporter created successfully")
        print(f"   ✓ Loaded {len(exporter.ir_reader.irs)} IRs from packaged dataset")
        
        # Test IR generation for each style
        print("\n2. Testing IR generation for each style...")
        for style in ["American", "British", "German", "Random"]:
            irs = exporter.generate_multiple_irs(count=2, style=style)
            print(f"   ✓ {style}: Generated {len(irs)} IRs")
            
            # Verify IR properties
            for i, ir in enumerate(irs):
                if len(ir) != 2048:
                    print(f"   ✗ IR {i+1} has wrong length: {len(ir)}")
                    return False
                if not (0.0 <= abs(ir.max()) <= 1.0):
                    print(f"   ✗ IR {i+1} has invalid amplitude: {ir.max()}")
                    return False
        
        # Test post-processing methods
        print("\n3. Testing post-processing methods...")
        test_ir = exporter.ir_reader.irs[0]
        
        processed_ir = exporter.apply_resonators(test_ir)
        print(f"   ✓ apply_resonators: {processed_ir.shape}")
        
        processed_ir = exporter.apply_fixed_filters(test_ir)
        print(f"   ✓ apply_fixed_filters: {processed_ir.shape}")
        
        processed_ir = exporter.apply_random_eq(test_ir)
        print(f"   ✓ apply_random_eq: {processed_ir.shape}")
        
        processed_ir = exporter.apply_amp_simulation(test_ir)
        print(f"   ✓ apply_amp_simulation: {processed_ir.shape}")
        
        processed_ir = exporter.suppress_unpleasant_resonances(test_ir)
        print(f"   ✓ suppress_unpleasant_resonances: {processed_ir.shape}")
        
        processed_ir = exporter.apply_style(test_ir, "American")
        print(f"   ✓ apply_style: {processed_ir.shape}")
        
        processed_ir = exporter.apply_high_shelf(test_ir)
        print(f"   ✓ apply_high_shelf: {processed_ir.shape}")
        
        # Test full processing pipeline
        print("\n4. Testing full processing pipeline...")
        processed_ir = exporter.process_ir(test_ir, style="British")
        print(f"   ✓ Full processing pipeline: {processed_ir.shape}")
        
        # Test DetailCanvas
        print("\n5. Testing DetailCanvas...")
        detail_canvas = main_module.DetailCanvas()
        detail_canvas.update_detail(test_ir)
        print(f"   ✓ DetailCanvas created and updated successfully")
        
        # Test IRGenerationWorker
        print("\n6. Testing IRGenerationWorker...")
        from PyQt5.QtCore import QObject
        worker = main_module.IRGenerationWorker(exporter, "Random", 3)
        print(f"   ✓ IRGenerationWorker created successfully")
        
        print(f"\n🎉 All main application components are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_launch():
    """Test that the GUI can be launched."""
    print("\n" + "=" * 50)
    print("Testing GUI Launch")
    print("=" * 50)
    
    try:
        print("1. Testing GUI imports...")
        from PyQt5.QtWidgets import QApplication
        
        # Test that we can create the main window class
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_app", "IR-Alchemist.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        print("   ✓ Main module loaded successfully")
        
        # Create QApplication (required for GUI)
        app = QApplication(sys.argv)
        print("   ✓ QApplication created")
        
        # Create main window
        window = main_module.IRGeneratorGUI()
        print("   ✓ Main window created successfully")
        
        # Check that the window has the expected components
        if hasattr(window, 'exporter'):
            print(f"   ✓ IRExporter initialized with {len(window.exporter.ir_reader.irs)} IRs")
        else:
            print("   ✗ IRExporter not found in main window")
            return False
        
        if hasattr(window, 'detail_canvas'):
            print("   ✓ DetailCanvas found")
        else:
            print("   ✗ DetailCanvas not found")
            return False
        
        print(f"\n✅ GUI can be launched successfully!")
        print(f"Run: python IR-Alchemist.py")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("IR-Alchemist Main Application Test Suite")
    print("=" * 60)
    
    # Check prerequisites
    if not os.path.exists("ir_dataset.irpkg"):
        print("❌ Package file 'ir_dataset.irpkg' not found!")
        return 1
    
    package_size = os.path.getsize("ir_dataset.irpkg")
    print(f"📦 Found package file: ir_dataset.irpkg ({package_size:,} bytes)")
    print()
    
    success = True
    
    # Test core components
    if not test_main_app_components():
        success = False
    
    # Test GUI launch
    if not test_gui_launch():
        success = False
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎉 All tests passed!")
        print("The main IR-Alchemist application is fully functional.")
        print("\nFeatures verified:")
        print("  ✓ Packaged dataset loading")
        print("  ✓ IR generation for all styles")
        print("  ✓ Post-processing pipeline")
        print("  ✓ GUI components")
        print("  ✓ DetailCanvas with fallback display")
        print("  ✓ Worker thread functionality")
        print("\nYou can now run: python IR-Alchemist.py")
    else:
        print("❌ Some tests failed!")
        print("Please check the errors above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
