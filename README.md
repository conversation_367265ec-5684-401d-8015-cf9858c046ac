# IR-Alchemist

IR-Alchemist is a cabinet impulse response generator for guitar and bass. It allows you to create, preview, and export high-quality impulse responses for use in your DAW or amp simulator.

## Features

- Generate cabinet impulse responses in different styles (American, British, German, Random)
- Preview IRs with real-time audio playback
- Visualize frequency response and spectrogram
- Export IRs as WAV files for use in your favorite amp simulator

## Dataset-Based Approach

IR-Alchemist now uses a dataset-based approach instead of machine learning. The application loads impulse responses from a directory of WAV files and applies post-processing to create unique variations.

### IR Dataset

The application looks for IR files in the following location:
```
E:\IRs\IR-Alchemist IRs
```

You can modify the path in the `irpkg_reader.py` file if needed.

## Usage

1. Select a cabinet style (American, British, German, or Random)
2. Click "Generate 10 IRs" to create a batch of impulse responses
3. Preview IRs by clicking the "Play" button
4. Export individual IRs or the entire batch

## Technical Details

IR-<PERSON><PERSON> applies various post-processing techniques to the base IRs:

- Resonators to simulate cabinet resonances
- Fixed filters for frequency shaping
- Random EQ for tonal variations
- Amp simulation for harmonic content
- Resonance suppression to reduce harsh frequencies
- Style-specific EQ curves

## Requirements

- Python 3.6+
- PyQt5
- NumPy
- SciPy
- Matplotlib (optional for visualizations)
- SoundFile

## Installation

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Run the application: `python IR-Alchemist.py`

## Simplified Version

A simplified version of the application is also available:
```
python IR-Alchemist-Simple.py
```

This version has a more streamlined interface and doesn't require matplotlib.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
