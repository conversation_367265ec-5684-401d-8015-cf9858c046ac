#!/usr/bin/env python3
"""
IR Dataset Packager for IR-Alchemist
Creates encrypted and compressed packages from IR directories.
"""

import os
import struct
import json
import zlib
import hashlib
import logging
from pathlib import Path
from typing import List, Dict, Any
import numpy as np
import soundfile as sf
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
import base64

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class IRDatasetPackager:
    """
    Packages IR files into an encrypted, compressed dataset file.
    """
    
    # File format constants
    MAGIC_HEADER = b'IRALCH'
    FORMAT_VERSION = 2
    DEFAULT_SAMPLE_RATE = 48000
    DEFAULT_IR_LENGTH = 2048
    
    def __init__(self, password: str = "IR-Alchemist-2024-Dataset"):
        """
        Initialize the packager with encryption settings.
        
        Args:
            password: Password for encryption (default uses a built-in key)
        """
        self.password = password.encode('utf-8')
        self.salt = b"IR-Alchemist-Salt-v2"
        self.styles = ["American", "British", "German", "Random"]
        
    def _derive_key(self) -> bytes:
        """Derive encryption key from password."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return key
    
    def _encrypt_data(self, data: bytes) -> bytes:
        """Encrypt data using Fernet symmetric encryption."""
        key = self._derive_key()
        f = Fernet(key)
        return f.encrypt(data)
    
    def _compress_data(self, data: bytes) -> bytes:
        """Compress data using zlib."""
        return zlib.compress(data, level=9)
    
    def _load_ir_file(self, file_path: Path) -> np.ndarray:
        """
        Load and process an IR file.
        
        Args:
            file_path: Path to the IR file
            
        Returns:
            Processed IR as numpy array
        """
        try:
            # Load audio data
            audio, sr = sf.read(str(file_path))
            
            # Convert to mono if stereo
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)
            
            # Resample if necessary (simple interpolation)
            if sr != self.DEFAULT_SAMPLE_RATE:
                orig_len = len(audio)
                target_len = int(orig_len * (self.DEFAULT_SAMPLE_RATE / sr))
                indices = np.linspace(0, orig_len - 1, target_len)
                audio = np.interp(indices, np.arange(orig_len), audio)
            
            # Ensure correct length
            if len(audio) > self.DEFAULT_IR_LENGTH:
                audio = audio[:self.DEFAULT_IR_LENGTH]
            elif len(audio) < self.DEFAULT_IR_LENGTH:
                audio = np.pad(audio, (0, self.DEFAULT_IR_LENGTH - len(audio)))
            
            # Normalize
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                audio = audio / max_val
            
            return audio.astype(np.float32)
            
        except Exception as e:
            logging.error(f"Error loading IR file {file_path}: {e}")
            return None
    
    def _categorize_ir_by_filename(self, filename: str) -> str:
        """
        Categorize IR by filename patterns.
        
        Args:
            filename: Name of the IR file
            
        Returns:
            Style category
        """
        filename_lower = filename.lower()
        
        # Check for style keywords in filename
        if any(word in filename_lower for word in ['american', 'usa', 'us', 'fender', 'mesa']):
            return "American"
        elif any(word in filename_lower for word in ['british', 'uk', 'brit', 'marshall', 'vox', 'orange']):
            return "British"
        elif any(word in filename_lower for word in ['german', 'germany', 'diezel', 'engl', 'bogner']):
            return "German"
        else:
            return "Random"
    
    def scan_ir_directory(self, ir_dir: str) -> Dict[str, List[Path]]:
        """
        Scan directory for IR files and categorize them.
        
        Args:
            ir_dir: Directory containing IR files
            
        Returns:
            Dictionary mapping styles to lists of file paths
        """
        if not os.path.exists(ir_dir):
            raise FileNotFoundError(f"IR directory not found: {ir_dir}")
        
        # Find all WAV files
        ir_files = []
        for ext in ['*.wav', '*.WAV']:
            ir_files.extend(list(Path(ir_dir).rglob(ext)))
        
        if not ir_files:
            raise ValueError(f"No IR files found in {ir_dir}")
        
        logging.info(f"Found {len(ir_files)} IR files in {ir_dir}")
        
        # Categorize files
        categorized = {style: [] for style in self.styles}
        
        for file_path in ir_files:
            style = self._categorize_ir_by_filename(file_path.name)
            categorized[style].append(file_path)
        
        # Log categorization results
        for style, files in categorized.items():
            logging.info(f"  {style}: {len(files)} files")
        
        return categorized
    
    def create_dataset_package(self, ir_dir: str, output_file: str) -> bool:
        """
        Create a packaged dataset from IR directory.
        
        Args:
            ir_dir: Source directory containing IR files
            output_file: Output package file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logging.info(f"Creating dataset package from {ir_dir}")
            
            # Scan and categorize IR files
            categorized_files = self.scan_ir_directory(ir_dir)
            
            # Load and process all IRs
            dataset = {
                "metadata": {
                    "version": self.FORMAT_VERSION,
                    "sample_rate": self.DEFAULT_SAMPLE_RATE,
                    "ir_length": self.DEFAULT_IR_LENGTH,
                    "styles": self.styles,
                    "total_irs": 0,
                    "created_by": "IR-Alchemist Dataset Packager",
                },
                "irs": {style: [] for style in self.styles}
            }
            
            total_loaded = 0
            
            for style, file_paths in categorized_files.items():
                logging.info(f"Processing {len(file_paths)} {style} IRs...")
                
                for file_path in file_paths:
                    ir_data = self._load_ir_file(file_path)
                    if ir_data is not None:
                        # Store IR with metadata (convert bytes to base64 for JSON serialization)
                        ir_entry = {
                            "data": base64.b64encode(ir_data.tobytes()).decode('ascii'),
                            "filename": file_path.name,
                            "original_path": str(file_path.relative_to(ir_dir))
                        }
                        dataset["irs"][style].append(ir_entry)
                        total_loaded += 1
                    
                    if total_loaded % 20 == 0:
                        logging.info(f"  Loaded {total_loaded} IRs...")
            
            dataset["metadata"]["total_irs"] = total_loaded
            logging.info(f"Successfully loaded {total_loaded} IRs")
            
            # Serialize dataset to JSON
            logging.info("Serializing dataset...")
            json_data = json.dumps(dataset, indent=None, separators=(',', ':')).encode('utf-8')
            
            # Compress the data
            logging.info("Compressing dataset...")
            compressed_data = self._compress_data(json_data)
            compression_ratio = len(compressed_data) / len(json_data)
            logging.info(f"Compression ratio: {compression_ratio:.2f} ({len(json_data)} -> {len(compressed_data)} bytes)")
            
            # Encrypt the compressed data
            logging.info("Encrypting dataset...")
            encrypted_data = self._encrypt_data(compressed_data)
            
            # Create file header
            header = struct.pack('<6sHII', 
                               self.MAGIC_HEADER,           # Magic header (6 bytes)
                               self.FORMAT_VERSION,         # Version (2 bytes)
                               len(encrypted_data),         # Data size (4 bytes)
                               zlib.crc32(encrypted_data)   # CRC32 checksum (4 bytes)
                               )
            
            # Write the package file
            logging.info(f"Writing package to {output_file}")
            with open(output_file, 'wb') as f:
                f.write(header)
                f.write(encrypted_data)
            
            # Calculate final file size
            file_size = os.path.getsize(output_file)
            logging.info(f"Package created successfully: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
            
            # Verify the package
            if self._verify_package(output_file):
                logging.info("Package verification successful!")
                return True
            else:
                logging.error("Package verification failed!")
                return False
                
        except Exception as e:
            logging.error(f"Error creating dataset package: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _verify_package(self, package_file: str) -> bool:
        """
        Verify that a package file can be read correctly.
        
        Args:
            package_file: Path to the package file
            
        Returns:
            True if verification successful
        """
        try:
            with open(package_file, 'rb') as f:
                # Read header
                header_data = f.read(16)
                if len(header_data) != 16:
                    return False
                
                magic, version, data_size, checksum = struct.unpack('<6sHII', header_data)
                
                if magic != self.MAGIC_HEADER:
                    return False
                
                if version != self.FORMAT_VERSION:
                    return False
                
                # Read encrypted data
                encrypted_data = f.read(data_size)
                if len(encrypted_data) != data_size:
                    return False
                
                # Verify checksum
                if zlib.crc32(encrypted_data) != checksum:
                    return False
                
                # Try to decrypt and decompress
                key = self._derive_key()
                fernet = Fernet(key)
                compressed_data = fernet.decrypt(encrypted_data)
                json_data = zlib.decompress(compressed_data)
                dataset = json.loads(json_data.decode('utf-8'))
                
                # Basic structure validation
                required_keys = ['metadata', 'irs']
                if not all(key in dataset for key in required_keys):
                    return False
                
                if not all(style in dataset['irs'] for style in self.styles):
                    return False
                
                return True
                
        except Exception as e:
            logging.error(f"Package verification error: {e}")
            return False

def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Package IR files into encrypted dataset')
    parser.add_argument('input_dir', help='Input directory containing IR files')
    parser.add_argument('output_file', help='Output package file path')
    parser.add_argument('--password', help='Encryption password (optional)')
    
    args = parser.parse_args()
    
    # Create packager
    packager = IRDatasetPackager(args.password) if args.password else IRDatasetPackager()
    
    # Create package
    success = packager.create_dataset_package(args.input_dir, args.output_file)
    
    if success:
        print(f"✅ Successfully created dataset package: {args.output_file}")
        return 0
    else:
        print("❌ Failed to create dataset package")
        return 1

if __name__ == "__main__":
    exit(main())
