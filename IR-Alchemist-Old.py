import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect,
    QComboBox, QProgressBar, QStatusBar, QMenuBar, QAction, QToolBar
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap, QKeySequence
import logging

# Import modern UI framework and stereo audio handler
from modern_ui_framework import *
from stereo_audio_handler import stereo_handler

# Using native PyQt5 visualization system
logging.info("Using native PyQt5 visualization system")

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# --- CarbonWidget: Custom widget with carbon background ---
class CarbonWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pattern = self.createCarbonPattern()

    def createCarbonPattern(self):
        # Create a 40x40 pixmap with a carbon-like pattern
        pixmap = QPixmap(40, 40)
        pixmap.fill(QColor("#2b2b2b"))  # Base dark color
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        whitePen = QPen(QColor(255, 255, 255, 15))  # ~6% opacity
        blackPen = QPen(QColor(0, 0, 0, 15))
        # Draw white lines
        painter.setPen(whitePen)
        painter.drawLine(0, 10, 30, 40)
        painter.drawLine(10, 0, 40, 30)
        # Draw black lines
        painter.setPen(blackPen)
        painter.drawLine(0, 20, 20, 40)
        painter.drawLine(20, 0, 40, 20)
        painter.end()
        return pixmap

    def paintEvent(self, event):
        painter = QPainter(self)
        brush = QBrush(self.pattern)
        painter.fillRect(self.rect(), brush)
        super().paintEvent(event)

# --- Custom Checkbox without SVG ---
class DarkCheckBox(QCheckBox):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #FFFFFF;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #555555;
                background-color: #333333;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #007ACC;
                background-color: #007ACC;
            }
        """)

# --- Detail Canvas for IR Preview ---
# Import native visualization system
from native_visualization import NativeDetailCanvas

class DetailCanvas(NativeDetailCanvas):
    """
    Native PyQt5-based detail canvas for IR visualization.
    Replaces matplotlib with custom PyQt5 graphics.
    """

    def __init__(self, parent=None, dpi=100):
        super().__init__(parent)
        # dpi parameter kept for compatibility but not used

# --- IRExporter ---
class IRExporter:
    def __init__(self, package_file=None):
        # Import the IR reader
        from irpkg_reader import IRPKGReader

        # Initialize the IR dataset reader with packaged dataset
        self.ir_reader = IRPKGReader(package_file)
        self.ir_length = 2048
        self.pp_params = {
            "resonators": {"min_count": 1, "max_count": 4, "gain_range": (0.8, 1.2)},
            "fixed_filters": {"hp_cutoff": 80, "lp_cutoff": 9500, "order": 3},
            "random_eq": {"num_points": 8, "gain_range": (0.5, 1.5)},
            "amp_simulation": {"drive": 1.5, "mid_band": (300, 3000), "mid_gain_range": (0.8, 1.2)},
            "suppress_resonances": {"threshold": 1.5, "window_size": 9},
            "high_shelf": {"cutoff": 4000, "shelf_gain": 1.2},
            "custom_style": {"bass_boost": 1.05, "mid_boost": 1.05, "high_boost": 1.05}
        }

    # ===== POST-PROCESSING METHODS (CURRENTLY UNUSED) =====
    # These methods are preserved for potential future use but are not applied
    # in the current implementation which returns raw IRs from the dataset.

    def apply_resonators(self, ir, sample_rate=48000):
        params = self.pp_params["resonators"]
        min_count = params["min_count"]
        max_count = params["max_count"]
        count = min_count if min_count == max_count else np.random.randint(min_count, max_count + 1)
        processed = np.copy(ir)
        for _ in range(count):
            fc = np.random.uniform(1000, 8000)
            Q = np.random.uniform(10, 30)
            bw = fc / Q
            low = max(fc - bw / 2, 20)
            high = min(fc + bw / 2, sample_rate / 2 - 1)
            low_norm = low / (sample_rate / 2)
            high_norm = high / (sample_rate / 2)
            try:
                b, a = butter(2, [low_norm, high_norm], btype='band')
            except ValueError as ve:
                logging.error("Error creating resonator filters: %s", str(ve))
                continue
            filtered = lfilter(b, a, ir)
            gain = np.random.uniform(*params["gain_range"])
            processed += gain * filtered
        return processed

    def apply_fixed_filters(self, ir, sample_rate=48048):
        params = self.pp_params["fixed_filters"]
        hp_norm = params["hp_cutoff"] / (sample_rate / 2)
        b_hp, a_hp = butter(params["order"], hp_norm, btype='high')
        ir_hp = lfilter(b_hp, a_hp, ir)
        lp_norm = params["lp_cutoff"] / (sample_rate / 2)
        b_lp, a_lp = butter(params["order"], lp_norm, btype='low')
        return lfilter(b_lp, a_lp, ir_hp)

    def apply_random_eq(self, ir, sample_rate=48048):
        params = self.pp_params["random_eq"]
        n = len(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        IR_fft = np.fft.rfft(ir)
        freq_points = np.linspace(80, 9500, num=params["num_points"])
        random_gains = np.random.uniform(*params["gain_range"], size=params["num_points"])
        gain_curve = np.interp(freqs, freq_points, random_gains)
        kernel = np.ones(3) / 3
        smooth_curve = np.convolve(gain_curve, kernel, mode='same')
        blended_curve = 0.5 * gain_curve + 0.5 * smooth_curve
        IR_fft *= blended_curve
        return np.fft.irfft(IR_fft, n=n)

    def apply_amp_simulation(self, ir, sample_rate=48048):
        params = self.pp_params["amp_simulation"]
        drive = params["drive"]
        saturated = np.tanh(drive * ir)
        b_mid, a_mid = butter(2, [params["mid_band"][0] / (sample_rate / 2),
                                   params["mid_band"][1] / (sample_rate / 2)], btype='band')
        mid = lfilter(b_mid, a_mid, saturated)
        mid_gain = np.random.uniform(*params["mid_gain_range"])
        mid *= mid_gain
        return 0.5 * saturated + 0.5 * mid

    def suppress_unpleasant_resonances(self, ir, sample_rate=48048):
        params = self.pp_params["suppress_resonances"]
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        mag = np.abs(IR_fft)
        phase = np.angle(IR_fft)
        kernel = np.ones(params["window_size"]) / params["window_size"]
        mag_smoothed = np.convolve(mag, kernel, mode='same')
        mag_new = np.minimum(mag, params["threshold"] * mag_smoothed)
        new_fft = mag_new * np.exp(1j * phase)
        return np.fft.irfft(new_fft, n=n)

    def apply_style(self, ir, style, sample_rate=48048):
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        boost = np.ones_like(IR_fft)
        if style == "German":
            boost[(freqs >= 4000) & (freqs <= 6000)] *= 1.10
        elif style == "British":
            boost[(freqs >= 300) & (freqs <= 800)] *= 1.15
        elif style == "American":
            boost[freqs < 300] *= 1.10
        elif style == "Random":
            # For "Random", use a random modulation of the boost curve
            boost = np.random.uniform(0.8, 1.2, size=boost.shape)
        new_fft = IR_fft * boost
        return np.fft.irfft(new_fft, n=n)

    def apply_high_shelf(self, ir, sample_rate=48048):
        params = self.pp_params["high_shelf"]
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        IR_fft[freqs >= params["cutoff"]] *= params["shelf_gain"]
        return np.fft.irfft(IR_fft, n=n)

    def process_ir(self, ir, style="American", sample_rate=48048):
        """
        UNUSED: Full IR processing pipeline with all effects.
        This method is preserved for potential future use but is not called
        in the current implementation which returns raw IRs.
        """
        ir = self.apply_resonators(ir, sample_rate)
        ir = self.apply_fixed_filters(ir, sample_rate)
        ir = self.apply_random_eq(ir, sample_rate)
        ir = self.apply_amp_simulation(ir, sample_rate)
        ir = self.suppress_unpleasant_resonances(ir, sample_rate)
        ir = self.apply_style(ir, style, sample_rate)
        ir = self.apply_high_shelf(ir, sample_rate)
        norm_factor = np.max(np.abs(ir)) + 1e-9
        return ir / norm_factor

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """
        Generate multiple IRs by randomly selecting from the dataset without any processing.

        Args:
            diversity (float): Ignored - kept for interface compatibility
            count (int): Number of IRs to return (default: 10)
            style (str): Ignored - random selection from entire dataset

        Returns:
            list: List of raw, unprocessed IR arrays from the dataset
        """
        # Ensure we have IRs available
        if not self.ir_reader.irs:
            logging.warning("No IRs available in dataset")
            return []

        # Calculate how many unique IRs we can provide
        total_irs = len(self.ir_reader.irs)
        unique_count = min(count, total_irs)

        # Randomly select unique IRs from the entire dataset (ignore style)
        import random
        selected_indices = random.sample(range(total_irs), unique_count)

        # Get the raw IRs without any processing
        raw_irs = [self.ir_reader.irs[i].copy() for i in selected_indices]

        # If we need more IRs than unique ones available, fill with additional random selections
        if count > unique_count:
            additional_needed = count - unique_count
            for _ in range(additional_needed):
                # Select random IR (may be duplicate now)
                random_index = random.randint(0, total_irs - 1)
                raw_irs.append(self.ir_reader.irs[random_index].copy())

        logging.info(f"Selected {len(raw_irs)} raw IRs from dataset ({unique_count} unique)")
        return raw_irs

# --- IR Generation Worker ---
class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, exporter, style, count, seed=None):
        super().__init__()
        self.exporter = exporter
        self.style = style
        self.count = count
        self.seed = seed

    @pyqtSlot()
    def run(self):
        try:
            if self.seed is not None:
                np.random.seed(self.seed)
            logging.info("Starting IR selection with style=%s, count=%d", self.style, self.count)
            irs = self.exporter.generate_multiple_irs(count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            logging.error("Error during IR selection: %s", str(e))
            self.error.emit(str(e))

# --- Main GUI Class ---
class IRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist - Professional Audio Processing")
        self.setGeometry(100, 100, 1600, 1000)

        # Apply Material Design theme
        MaterialTheme.apply_global_style(QApplication.instance())

        # Initialize data
        self.last_generated_irs = None
        self.ir_widgets = []
        self.selected_style = "American"
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.current_temp_file = None

        self.player.mediaStatusChanged.connect(self._on_media_status_changed)

        # Create modern UI
        self.create_modern_interface()

    def create_modern_interface(self):
        """Create the modern Material Design interface."""
        # Set main window background
        self.setStyleSheet(f"background-color: {MaterialColors.BACKGROUND};")

        # Create central widget
        central_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(24)

        # Create header card
        header_card = self.create_header_card()
        main_layout.addWidget(header_card)

        # Create main content area
        content_layout = QHBoxLayout()
        content_layout.setSpacing(24)

        # Left panel - Controls
        controls_card = self.create_controls_card()
        controls_card.setMaximumWidth(400)
        content_layout.addWidget(controls_card)

        # Right panel - Visualization and IR list
        right_panel = self.create_right_panel()
        content_layout.addWidget(right_panel, 1)

        main_layout.addLayout(content_layout)

        # Create status bar
        self.create_status_bar()

        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def create_header_card(self):
        """Create the modern header card."""
        header_card = MaterialCard(elevation=2)
        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(32, 24, 32, 24)
        header_layout.setSpacing(16)

        # Title section
        title_layout = QHBoxLayout()
        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet(f"""
            font-size: 32px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            font-family: 'Segoe UI', 'Roboto', sans-serif;
        """)

        company_label = QLabel("by Develop Device")
        company_label.setStyleSheet(f"""
            font-size: 16px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            font-style: italic;
            margin-left: 16px;
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(company_label)
        title_layout.addStretch()
        header_layout.addLayout(title_layout)

        # Subtitle
        subtitle_label = QLabel("Professional Cabinet IR Generator with Stereo Audio Support")
        subtitle_label.setStyleSheet(f"""
            font-size: 18px;
            color: {MaterialColors.PRIMARY};
            font-weight: 500;
        """)
        header_layout.addWidget(subtitle_label)

        # Description
        description = QLabel(
            "Generate high-quality impulse responses from our curated dataset. "
            "Select characteristics, generate IRs, and preview with stereo audio processing."
        )
        description.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            line-height: 1.5;
        """)
        description.setWordWrap(True)
        header_layout.addWidget(description)

        header_card.setLayout(header_layout)
        return header_card

    def create_controls_card(self):
        """Create the controls card with Material Design components."""
        controls_card = MaterialCard(elevation=1)
        controls_layout = QVBoxLayout()
        controls_layout.setContentsMargins(24, 24, 24, 24)
        controls_layout.setSpacing(24)

        # Card title
        title_label = QLabel("IR Generation Controls")
        title_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            margin-bottom: 8px;
        """)
        controls_layout.addWidget(title_label)

        # Style selection section
        style_section = self.create_style_selection_section()
        controls_layout.addWidget(style_section)

        # Generation controls
        generation_section = self.create_generation_section()
        controls_layout.addWidget(generation_section)

        # Export controls
        export_section = self.create_export_section()
        controls_layout.addWidget(export_section)

        controls_layout.addStretch()
        controls_card.setLayout(controls_layout)
        return controls_card

    def create_style_selection_section(self):
        """Create the style selection section."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"background-color: {MaterialColors.SURFACE_VARIANT};")

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(16, 16, 16, 16)
        section_layout.setSpacing(12)

        # Section title
        section_title = QLabel("Sound Characteristic")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
        """)
        section_layout.addWidget(section_title)

        # Style radio buttons with modern styling
        self.style_group = QButtonGroup()
        styles = ["American", "British", "German", "Random"]

        for i, style in enumerate(styles):
            radio = QRadioButton(style)
            radio.setStyleSheet(f"""
                QRadioButton {{
                    font-size: 14px;
                    color: {MaterialColors.ON_SURFACE};
                    spacing: 8px;
                    padding: 8px;
                }}
                QRadioButton::indicator {{
                    width: 20px;
                    height: 20px;
                    border-radius: 10px;
                    border: 2px solid {MaterialColors.ON_SURFACE_VARIANT};
                }}
                QRadioButton::indicator:checked {{
                    background-color: {MaterialColors.PRIMARY};
                    border: 2px solid {MaterialColors.PRIMARY};
                }}
                QRadioButton::indicator:checked:after {{
                    content: '';
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    background-color: {MaterialColors.ON_PRIMARY};
                    margin: 4px;
                }}
            """)

            if style == "American":
                radio.setChecked(True)
                self.selected_style = style

            radio.toggled.connect(lambda checked, s=style: self.on_style_changed(s, checked))
            self.style_group.addButton(radio)
            section_layout.addWidget(radio)

        section_card.setLayout(section_layout)
        return section_card

    def create_generation_section(self):
        """Create the generation controls section."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"background-color: {MaterialColors.SURFACE_VARIANT};")

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(16, 16, 16, 16)
        section_layout.setSpacing(16)

        # Section title
        section_title = QLabel("Generation")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
        """)
        section_layout.addWidget(section_title)

        # Generate button
        self.generate_button = MaterialButton("Generate IRs", "filled")
        self.generate_button.setMinimumHeight(48)
        self.generate_button.clicked.connect(self.generate_irs)
        section_layout.addWidget(self.generate_button)

        # Progress bar
        self.progress_bar = MaterialProgressBar()
        self.progress_bar.setVisible(False)
        section_layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready to generate IRs")
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            padding: 8px;
        """)
        section_layout.addWidget(self.status_label)

        section_card.setLayout(section_layout)
        return section_card

    def create_export_section(self):
        """Create the export controls section."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"background-color: {MaterialColors.SURFACE_VARIANT};")

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(16, 16, 16, 16)
        section_layout.setSpacing(12)

        # Section title
        section_title = QLabel("Export")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
        """)
        section_layout.addWidget(section_title)

        # Export buttons
        self.export_individual_button = MaterialButton("Export Individual", "outlined")
        self.export_individual_button.clicked.connect(self.export_individual_irs)
        self.export_individual_button.setEnabled(False)
        section_layout.addWidget(self.export_individual_button)

        self.export_combined_button = MaterialButton("Export Combined", "outlined")
        self.export_combined_button.clicked.connect(self.export_combined_ir)
        self.export_combined_button.setEnabled(False)
        section_layout.addWidget(self.export_combined_button)

        section_card.setLayout(section_layout)
        return section_card
    def create_right_panel(self):
        """Create the right panel with IR list and visualization."""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(24)

        # IR List section
        ir_list_card = self.create_ir_list_card()
        right_layout.addWidget(ir_list_card)

        # Visualization section
        viz_card = self.create_visualization_card()
        right_layout.addWidget(viz_card, 1)

        right_widget.setLayout(right_layout)
        return right_widget

    def create_ir_list_card(self):
        """Create the IR list card."""
        ir_card = MaterialCard(elevation=1)
        ir_layout = QVBoxLayout()
        ir_layout.setContentsMargins(24, 24, 24, 24)
        ir_layout.setSpacing(16)

        # Title and controls
        title_layout = QHBoxLayout()
        title_label = QLabel("Generated IRs")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # Play button
        self.play_button = MaterialButton("▶ Preview", "text")
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.preview_selected_ir)
        title_layout.addWidget(self.play_button)

        ir_layout.addLayout(title_layout)

        # IR list container
        self.ir_list_widget = QWidget()
        self.ir_list_layout = QVBoxLayout()
        self.ir_list_layout.setContentsMargins(0, 0, 0, 0)
        self.ir_list_layout.setSpacing(8)
        self.ir_list_widget.setLayout(self.ir_list_layout)

        # Scroll area for IR list
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.ir_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                background-color: {MaterialColors.SURFACE};
            }}
        """)
        ir_layout.addWidget(scroll_area)

        ir_card.setLayout(ir_layout)
        return ir_card

    def create_visualization_card(self):
        """Create the visualization card."""
        viz_card = MaterialCard(elevation=1)
        viz_layout = QVBoxLayout()
        viz_layout.setContentsMargins(24, 24, 24, 24)
        viz_layout.setSpacing(16)

        # Title
        title_label = QLabel("IR Analysis & Visualization")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
        """)
        viz_layout.addWidget(title_label)

        # Detail canvas
        self.detail_canvas = DetailCanvas()
        viz_layout.addWidget(self.detail_canvas)

        viz_card.setLayout(viz_layout)
        return viz_card

    def create_status_bar(self):
        """Create the modern status bar."""
        status_bar = QStatusBar()
        status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE_VARIANT};
                border-top: 1px solid {MaterialColors.SURFACE_VARIANT};
                font-size: 12px;
                padding: 8px;
            }}
        """)
        status_bar.showMessage("Ready - Select characteristics and generate IRs")
        self.setStatusBar(status_bar)

    def on_style_changed(self, style, checked):
        """Handle style selection change."""
        if checked:
            self.selected_style = style
            self.status_label.setText(f"Style: {style} - Ready to generate")

    def update_ir_list_display(self):
        """Update the IR list display with modern cards."""
        # Clear existing widgets
        for i in reversed(range(self.ir_list_layout.count())):
            child = self.ir_list_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.last_generated_irs:
            return

        # Create IR cards
        for i, ir in enumerate(self.last_generated_irs):
            ir_card = self.create_ir_item_card(i, ir)
            self.ir_list_layout.addWidget(ir_card)

    def create_ir_item_card(self, index, ir):
        """Create a card for an individual IR item."""
        item_card = MaterialCard(elevation=0)
        item_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER};
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
        """)
        item_card.setMaximumHeight(60)

        layout = QHBoxLayout()
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)

        # IR info
        info_label = QLabel(f"IR {index + 1}")
        info_label.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
        """)
        layout.addWidget(info_label)

        # IR stats
        rms = np.sqrt(np.mean(ir**2))
        peak = np.max(np.abs(ir))
        stats_label = QLabel(f"RMS: {rms:.4f} | Peak: {peak:.4f}")
        stats_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
        """)
        layout.addWidget(stats_label)

        layout.addStretch()

        # Select button
        select_btn = MaterialButton("Select", "text")
        select_btn.clicked.connect(lambda: self.select_ir(index))
        layout.addWidget(select_btn)

        item_card.setLayout(layout)
        return item_card

    # Initialize the IR exporter
    def initialize_exporter(self):
        """Initialize the IR exporter."""
        try:
            self.exporter = IRExporter()
            self.statusBar().showMessage(f"Ready - Loaded {len(self.exporter.ir_reader.irs)} IRs from dataset")
        except Exception as e:
            self.show_message("Error", f"Failed to initialize IR exporter: {str(e)}")

    # Event handlers and methods
    def generate_irs(self):
        """Generate IRs using the modern interface."""
        if self.worker_thread and self.worker_thread.isRunning():
            return

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.generate_button.setEnabled(False)
        self.status_label.setText("Generating IRs...")

        # Create worker thread
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(self.exporter, self.selected_style, 10)
        self.worker.moveToThread(self.worker_thread)

        # Connect signals
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)

        # Start generation
        self.worker_thread.start()

    def on_generation_finished(self, irs):
        """Handle generation completion."""
        self.last_generated_irs = irs
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.export_individual_button.setEnabled(True)
        self.export_combined_button.setEnabled(True)
        self.play_button.setEnabled(True)

        self.status_label.setText(f"Generated {len(irs)} IRs successfully")
        self.statusBar().showMessage(f"Generated {len(irs)} IRs - Ready for preview and export")

        # Update IR list display
        self.update_ir_list_display()

        # Select first IR by default
        if irs:
            self.select_ir(0)

    def on_generation_error(self, error_msg):
        """Handle generation error."""
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.status_label.setText("Generation failed")
        self.show_message("Error", f"IR generation failed: {error_msg}")

    def select_ir(self, index):
        """Select an IR for preview and visualization."""
        if not self.last_generated_irs or index >= len(self.last_generated_irs):
            return

        ir = self.last_generated_irs[index]
        self.detail_canvas.update_detail(ir, 48000)
        self.selected_ir_index = index
        self.statusBar().showMessage(f"Selected IR {index + 1} - Ready for preview")

    def preview_selected_ir(self):
        """Preview the selected IR."""
        if not hasattr(self, 'selected_ir_index') or not self.last_generated_irs:
            return

        ir = self.last_generated_irs[self.selected_ir_index]
        self._preview_ir(ir)

        # Initialize the exporter
        self.initialize_exporter()

    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    # Export methods
    def export_individual_irs(self):
        """Export individual IRs."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        folder = QFileDialog.getExistingDirectory(self, "Select Export Folder")
        if not folder:
            return

        try:
            for i, ir in enumerate(self.last_generated_irs):
                filename = self.get_unique_filename(folder, f"IR_{i+1:02d}.wav")
                stereo_handler.save_audio(ir, filename, 48000, 'PCM_24')

            self.show_message("Success", f"Exported {len(self.last_generated_irs)} IRs to {folder}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def export_combined_ir(self):
        """Export combined IR."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        filename, _ = QFileDialog.getSaveFileName(self, "Save Combined IR", "Combined_IR.wav", "WAV files (*.wav)")
        if not filename:
            return

        try:
            # Average all IRs
            combined_ir = np.mean(self.last_generated_irs, axis=0)
            stereo_handler.save_audio(combined_ir, filename, 48000, 'PCM_24')
            self.show_message("Success", f"Combined IR exported to {filename}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    # Utility methods
    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    def show_message(self, title, message):
        """Show a message dialog."""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE};
            }}
            QMessageBox QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }}
        """)
        msg_box.exec_()

# Remove all old methods - they are replaced by the modern UI methods above
        for i in reversed(range(self.checkbox_grid.count())):
            widget = self.checkbox_grid.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        self.ir_widgets = []
        total = len(self.last_generated_irs)
        rows = 5
        cols = (total + rows - 1) // rows
        for i in range(total):
            container = QWidget()
            h_layout = QHBoxLayout(container)
            checkbox = DarkCheckBox(f"IR {i+1}")
            checkbox.setToolTip(f"Select IR {i+1}")
            preview_btn = QPushButton("Preview")
            preview_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #000000;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 5px 10px;
                }
                QPushButton:hover {
                    background-color: #F0F0F0;
                }
                QPushButton:pressed {
                    background-color: #E0E0E0;
                }
            """)
            preview_btn.setToolTip("Play audio preview of this impulse response")
            preview_btn.clicked.connect(lambda checked, idx=i: self.preview_single_ir(idx))
            h_layout.addWidget(checkbox)
            h_layout.addStretch()
            h_layout.addWidget(preview_btn)
            row = i % rows
            col = i // rows
            self.checkbox_grid.addWidget(container, row, col)
            self.ir_widgets.append({'checkbox': checkbox, 'preview_button': preview_btn})

    def save_selected_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "No IR selected for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in selected_indices:
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                # Use stereo handler for proper format preservation
                stereo_handler.save_audio(self.last_generated_irs[i], filename, 48000, 'PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "Selected IRs were successfully exported as WAV files.")

    def save_all_irs(self):
        if not self.last_generated_irs:
            self.show_message("Warning", "No IR available for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in range(len(self.last_generated_irs)):
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                # Use stereo handler for proper format preservation
                stereo_handler.save_audio(self.last_generated_irs[i], filename, 48000, 'PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "All IRs were successfully exported as WAV files.")

    def export_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if len(selected_indices) < 1:
            self.show_message("Warning", "Select at least one IR to export combined IR.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export combined IR")
        if not folder:
            return
        base_filename = "Combined_IR.wav"
        filename = self.get_unique_filename(folder, base_filename)
        try:
            # Use stereo handler for proper format preservation
            stereo_handler.save_audio(combined_ir, filename, 48000, 'PCM_24')
        except Exception as e:
            self.show_message("Error", f"Error exporting combined IR: {str(e)}")
            return
        self.show_message("Success", "Combined IR was successfully exported as a WAV file.")

    def preview_single_ir(self, idx):
        if idx < 0 or idx >= len(self.last_generated_irs):
            self.show_message("Warning", "The selected IR is not valid.")
            return
        ir = self.last_generated_irs[idx]
        self._preview_ir(ir)
        self.detail_canvas.update_detail(ir)

    def preview_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "Select at least one IR for combined preview.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        self._preview_ir(combined_ir)
        self.detail_canvas.update_detail(combined_ir)

    def _preview_ir(self, ir):
        """Enhanced IR preview with stereo audio support."""
        sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
        try:
            # Load sample with stereo detection
            sample_data, sample_rate, channels = stereo_handler.load_audio(sample_path)
            logging.info(f"Loaded sample: {channels} channels, {sample_rate}Hz")
        except Exception as e:
            self.show_message("Error", f"Error loading sample file: {str(e)}")
            return

        try:
            # Perform stereo-aware convolution
            if len(sample_data.shape) > 1:  # Stereo sample
                convolved = stereo_handler.stereo_convolution(sample_data, ir, mode="stereo")
            else:  # Mono sample
                convolved = fftconvolve(sample_data, ir, mode="full")

            # Normalize while preserving stereo balance
            convolved = stereo_handler.normalize_audio(convolved, target_level=0.9)

        except Exception as e:
            self.show_message("Error", f"Error processing audio: {str(e)}")
            return

        # Clean up previous temp file
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
            self.current_temp_file = None

        # Create new temp file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_filename = temp_file.name
        temp_file.close()

        try:
            # Save with stereo format preservation
            stereo_handler.save_audio(convolved, temp_filename, sample_rate)
        except Exception as e:
            self.show_message("Error", f"Error exporting preview file: {str(e)}")
            return

        self.current_temp_file = temp_filename
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(temp_filename)))
        self.player.play()

    def _on_media_status_changed(self, status):
        if status in (QMediaPlayer.EndOfMedia, QMediaPlayer.InvalidMedia, QMediaPlayer.NoMedia):
            if self.current_temp_file and os.path.exists(self.current_temp_file):
                try:
                    os.remove(self.current_temp_file)
                except Exception:
                    pass
                self.current_temp_file = None

    def stop_preview(self):
        self.player.stop()

    def show_message(self, title, message):
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet("QMessageBox {background-color: #121212; color: #FFFFFF;} QMessageBox QLabel {color: #FFFFFF;} QPushButton { background-color: #333333; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; font-weight: bold; font-size: 14px; padding: 8px 12px;} QPushButton:hover {background-color: #444444;} QPushButton:pressed { background-color: #555555;}")
        msg_box.exec_()

    def closeEvent(self, event):
        if self.worker_thread is not None:
            try:
                if self.worker_thread.isRunning():
                    self.worker_thread.quit()
                    self.worker_thread.wait()
            except RuntimeError:
                pass
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
        event.accept()


if __name__ == "__main__":
    try:
        app = QApplication(sys.argv)
        font_path = os.path.join(os.path.dirname(__file__), "Exo-VariableFont_wght.ttf")
        font_id = QFontDatabase.addApplicationFont(font_path)
        if font_id != -1:
            families = QFontDatabase.applicationFontFamilies(font_id)
            if families:
                default_font = QFont(families[0])
                app.setFont(default_font)
                print("Loaded font:", families[0])
        else:
            print("Failed to load Exo-VariableFont_wght.ttf")

        print("Creating IRGeneratorGUI...")
        window = IRGeneratorGUI()
        print("Showing window...")
        window.show()
        print("Starting application...")
        sys.exit(app.exec_())
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()
