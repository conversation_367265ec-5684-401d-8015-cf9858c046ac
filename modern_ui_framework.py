#!/usr/bin/env python3
"""
Modern UI Framework for IR-Alchemist
Implements Material Design principles and contemporary UI/UX standards.
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import math

class MaterialColors:
    """Material Design 3.0 color palette with dark theme support."""

    # Primary colors
    PRIMARY = "#6750A4"
    PRIMARY_VARIANT = "#7F39FB"
    SECONDARY = "#625B71"
    SECONDARY_VARIANT = "#7D5260"

    # Surface colors (Dark theme)
    SURFACE = "#1C1B1F"
    SURFACE_VARIANT = "#49454F"
    SURFACE_CONTAINER = "#211F26"
    SURFACE_CONTAINER_HIGH = "#2B2930"
    SURFACE_CONTAINER_HIGHEST = "#36343B"

    # Background
    BACKGROUND = "#141218"

    # Text colors
    ON_SURFACE = "#E6E0E9"
    ON_SURFACE_VARIANT = "#CAC4D0"
    ON_PRIMARY = "#FFFFFF"
    ON_SECONDARY = "#FFFFFF"

    # Accent colors
    ACCENT_BLUE = "#4285F4"
    ACCENT_GREEN = "#34A853"
    ACCENT_ORANGE = "#FF9800"
    ACCENT_RED = "#EA4335"

    # State colors
    HOVER = "#FFFFFF1A"  # 10% white
    FOCUS = "#FFFFFF33"  # 20% white
    PRESSED = "#FFFFFF4D"  # 30% white
    DISABLED = "#FFFFFF61"  # 38% white

class MaterialButton(QPushButton):
    """Material Design button with elevation and ripple effects."""

    def __init__(self, text="", button_type="filled", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type  # "filled", "outlined", "text"
        self.elevation = 1
        self.is_hovered = False
        self.is_pressed = False

        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10, QFont.Medium))
        self.setCursor(Qt.PointingHandCursor)

        # Enable focus and ensure proper interaction
        self.setFocusPolicy(Qt.StrongFocus)
        self.setAttribute(Qt.WA_Hover, True)

        self._setup_style()

    def _setup_style(self):
        """Setup button styling based on type."""
        if self.button_type == "filled":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {MaterialColors.PRIMARY};
                    color: {MaterialColors.ON_PRIMARY};
                    border: none;
                    border-radius: 20px;
                    padding: 10px 24px;
                    font-weight: 500;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: {MaterialColors.PRIMARY_VARIANT};
                }}
                QPushButton:pressed {{
                    background-color: {MaterialColors.SECONDARY};
                }}
                QPushButton:focus {{
                    background-color: {MaterialColors.PRIMARY_VARIANT};
                    outline: 2px solid {MaterialColors.FOCUS};
                }}
                QPushButton:disabled {{
                    background-color: {MaterialColors.DISABLED};
                    color: {MaterialColors.ON_SURFACE_VARIANT};
                }}
            """)
        elif self.button_type == "outlined":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {MaterialColors.PRIMARY};
                    border: 2px solid {MaterialColors.PRIMARY};
                    border-radius: 20px;
                    padding: 8px 22px;
                    font-weight: 500;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: {MaterialColors.HOVER};
                    border-color: {MaterialColors.PRIMARY_VARIANT};
                }}
                QPushButton:pressed {{
                    background-color: {MaterialColors.PRESSED};
                }}
                QPushButton:focus {{
                    outline: 2px solid {MaterialColors.FOCUS};
                }}
                QPushButton:disabled {{
                    color: {MaterialColors.DISABLED};
                    border-color: {MaterialColors.DISABLED};
                }}
            """)
        else:  # text button
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {MaterialColors.PRIMARY};
                    border: none;
                    border-radius: 20px;
                    padding: 10px 12px;
                    font-weight: 500;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: {MaterialColors.HOVER};
                }}
                QPushButton:pressed {{
                    background-color: {MaterialColors.PRESSED};
                }}
                QPushButton:focus {{
                    outline: 2px solid {MaterialColors.FOCUS};
                }}
                QPushButton:disabled {{
                    color: {MaterialColors.DISABLED};
                }}
            """)

    def enterEvent(self, event):
        """Handle mouse enter event."""
        self.is_hovered = True
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event."""
        self.is_hovered = False
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """Handle mouse press event."""
        self.is_pressed = True
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release event."""
        self.is_pressed = False
        super().mouseReleaseEvent(event)

class MaterialRadioButton(QRadioButton):
    """Material Design radio button with proper styling and animations."""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)

        self.setFont(QFont("Segoe UI", 10))
        self.setCursor(Qt.PointingHandCursor)
        self.setFocusPolicy(Qt.StrongFocus)
        self.setAttribute(Qt.WA_Hover, True)

        # Animation properties
        self.is_hovered = False
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._update_animation)

        self.setStyleSheet(f"""
            QRadioButton {{
                font-size: 14px;
                color: {MaterialColors.ON_SURFACE};
                spacing: 12px;
                padding: 8px 12px;
                border: none;
                margin: 0;
                border-radius: 4px;
            }}
            QRadioButton:hover {{
                background-color: {MaterialColors.HOVER};
            }}
            QRadioButton::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid {MaterialColors.ON_SURFACE_VARIANT};
                background-color: transparent;
            }}
            QRadioButton::indicator:hover {{
                border: 2px solid {MaterialColors.PRIMARY};
                background-color: {MaterialColors.HOVER};
            }}
            QRadioButton::indicator:checked {{
                background-color: {MaterialColors.PRIMARY};
                border: 2px solid {MaterialColors.PRIMARY};
            }}
            QRadioButton::indicator:checked:hover {{
                background-color: {MaterialColors.PRIMARY_VARIANT};
                border: 2px solid {MaterialColors.PRIMARY_VARIANT};
            }}
            QRadioButton::indicator:focus {{
                border: 2px solid {MaterialColors.PRIMARY};
                outline: 2px solid {MaterialColors.FOCUS};
            }}
            QRadioButton:disabled {{
                color: {MaterialColors.DISABLED};
                background-color: transparent;
            }}
            QRadioButton::indicator:disabled {{
                border: 2px solid {MaterialColors.DISABLED};
                background-color: transparent;
            }}
        """)

    def enterEvent(self, event):
        """Handle mouse enter event."""
        self.is_hovered = True
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event."""
        self.is_hovered = False
        super().leaveEvent(event)

    def _update_animation(self):
        """Update animation state."""
        # Simple animation placeholder - could be enhanced with property animations
        pass

class MaterialCard(QFrame):
    """Material Design card with elevation and rounded corners."""

    def __init__(self, elevation=1, parent=None, interactive=False):
        super().__init__(parent)
        self.elevation = elevation
        self.base_elevation = elevation
        self.interactive = interactive
        self.is_hovered = False

        self.setFrameStyle(QFrame.NoFrame)

        # Enable hover events if interactive
        if self.interactive:
            self.setAttribute(Qt.WA_Hover, True)
            self.setCursor(Qt.PointingHandCursor)

        self._update_style()
        self._setup_shadow()

        # Animation timer for elevation changes (simplified approach)
        self.elevation_timer = QTimer()
        self.elevation_timer.setSingleShot(True)
        self.elevation_timer.timeout.connect(self._setup_shadow)

    def _update_style(self):
        """Update card styling."""
        hover_style = ""
        if self.interactive and self.is_hovered:
            hover_style = f"background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};"

        self.setStyleSheet(f"""
            QFrame {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                border-radius: 12px;
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                {hover_style}
            }}
        """)

    def _setup_shadow(self):
        """Setup shadow effect based on elevation."""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(self.elevation * 4)
        shadow.setOffset(0, self.elevation * 2)
        shadow.setColor(QColor(0, 0, 0, min(80, 40 + self.elevation * 10)))
        self.setGraphicsEffect(shadow)

    def enterEvent(self, event):
        """Handle mouse enter event."""
        if self.interactive:
            self.is_hovered = True
            self._animate_elevation(self.base_elevation + 2)
            self._update_style()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event."""
        if self.interactive:
            self.is_hovered = False
            self._animate_elevation(self.base_elevation)
            self._update_style()
        super().leaveEvent(event)

    def _animate_elevation(self, target_elevation):
        """Animate elevation change (simplified)."""
        self.elevation = target_elevation
        self.elevation_timer.start(100)  # Small delay for smooth transition

    def set_elevation(self, elevation):
        """Set card elevation."""
        self.elevation = elevation
        self.base_elevation = elevation
        self._setup_shadow()

class MaterialTextField(QLineEdit):
    """Material Design text field with floating label."""
    
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        self.placeholder_text = placeholder
        
        self.setMinimumHeight(56)
        self.setFont(QFont("Segoe UI", 10))
        
        self.setStyleSheet(f"""
            QLineEdit {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 4px;
                padding: 16px 12px;
                font-size: 16px;
            }}
            QLineEdit:focus {{
                border: 2px solid {MaterialColors.PRIMARY};
                background-color: {MaterialColors.SURFACE_CONTAINER};
            }}
            QLineEdit:hover {{
                border: 1px solid {MaterialColors.ON_SURFACE_VARIANT};
            }}
        """)
        
        self.setPlaceholderText(placeholder)

class MaterialProgressBar(QProgressBar):
    """Material Design progress bar with smooth animations."""

    def __init__(self, parent=None):
        super().__init__(parent)

        self.setMinimumHeight(4)
        self.setMaximumHeight(4)
        self.setTextVisible(False)

        # Animation properties
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._update_indeterminate)
        self.indeterminate_position = 0
        self.is_indeterminate = False

        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                border-radius: 2px;
                border: none;
            }}
            QProgressBar::chunk {{
                background-color: {MaterialColors.PRIMARY};
                border-radius: 2px;
            }}
        """)

    def setRange(self, minimum, maximum):
        """Override setRange to handle indeterminate state."""
        super().setRange(minimum, maximum)

        # Enable indeterminate animation when range is 0,0
        if minimum == 0 and maximum == 0:
            self.is_indeterminate = True
            self.animation_timer.start(50)  # 20 FPS
        else:
            self.is_indeterminate = False
            self.animation_timer.stop()

    def _update_indeterminate(self):
        """Update indeterminate progress animation."""
        if self.is_indeterminate:
            self.indeterminate_position = (self.indeterminate_position + 2) % 100
            # This creates a simple indeterminate animation
            # In a real implementation, you might want to use custom painting
            self.setValue(self.indeterminate_position)

    def hideEvent(self, event):
        """Stop animation when hidden."""
        self.animation_timer.stop()
        super().hideEvent(event)

    def showEvent(self, event):
        """Restart animation when shown if indeterminate."""
        if self.is_indeterminate:
            self.animation_timer.start(50)
        super().showEvent(event)

class MaterialSwitch(QCheckBox):
    """Material Design switch component."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setMinimumSize(52, 32)
        self.setMaximumSize(52, 32)
        
        self.setStyleSheet(f"""
            QCheckBox {{
                spacing: 0px;
            }}
            QCheckBox::indicator {{
                width: 52px;
                height: 32px;
                border-radius: 16px;
                background-color: {MaterialColors.SURFACE_VARIANT};
                border: 2px solid {MaterialColors.SURFACE_VARIANT};
            }}
            QCheckBox::indicator:checked {{
                background-color: {MaterialColors.PRIMARY};
                border: 2px solid {MaterialColors.PRIMARY};
            }}
            QCheckBox::indicator::handle {{
                width: 20px;
                height: 20px;
                border-radius: 10px;
                background-color: {MaterialColors.ON_SURFACE_VARIANT};
                margin: 6px;
            }}
            QCheckBox::indicator:checked::handle {{
                background-color: {MaterialColors.ON_PRIMARY};
                margin-left: 26px;
            }}
        """)

class MaterialTooltip(QToolTip):
    """Enhanced tooltip with Material Design styling."""
    
    @staticmethod
    def setup_global_style():
        """Setup global tooltip styling."""
        QToolTip.setFont(QFont("Segoe UI", 9))
        
        style = f"""
            QToolTip {{
                background-color: {MaterialColors.SURFACE_CONTAINER_HIGHEST};
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 12px;
            }}
        """
        
        return style

class MaterialSnackbar(QWidget):
    """Material Design snackbar for notifications."""
    
    def __init__(self, message, action_text=None, parent=None):
        super().__init__(parent)
        self.message = message
        self.action_text = action_text
        
        self.setFixedHeight(48)
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
                border-radius: 4px;
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
            }}
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(16, 0, 8, 0)
        
        # Message label
        message_label = QLabel(message)
        message_label.setFont(QFont("Segoe UI", 10))
        message_label.setStyleSheet(f"color: {MaterialColors.ON_SURFACE};")
        layout.addWidget(message_label)
        
        layout.addStretch()
        
        # Action button (optional)
        if action_text:
            action_btn = MaterialButton(action_text, "text")
            action_btn.setMaximumHeight(36)
            layout.addWidget(action_btn)
        
        self.setLayout(layout)
        
        # Auto-hide timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.hide)
        self.timer.setSingleShot(True)
    
    def show_message(self, duration=4000):
        """Show the snackbar for specified duration."""
        self.show()
        self.timer.start(duration)

class ResponsiveGridLayout(QGridLayout):
    """Responsive grid layout system with Material Design breakpoints."""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Material Design breakpoints (in pixels)
        self.breakpoints = {
            'xs': 0,      # Extra small devices
            'sm': 600,    # Small devices
            'md': 960,    # Medium devices
            'lg': 1280,   # Large devices
            'xl': 1920,   # Extra large devices
            'xxl': 2560   # Ultra-wide displays
        }

        # Default spacing for FullHD and above
        self.setSpacing(24)
        self.setContentsMargins(24, 24, 24, 24)

        # Track current breakpoint
        self.current_breakpoint = 'lg'

        # Store responsive configurations
        self.responsive_configs = {}

    def get_current_breakpoint(self, width):
        """Determine current breakpoint based on width."""
        if width >= self.breakpoints['xxl']:
            return 'xxl'
        elif width >= self.breakpoints['xl']:
            return 'xl'
        elif width >= self.breakpoints['lg']:
            return 'lg'
        elif width >= self.breakpoints['md']:
            return 'md'
        elif width >= self.breakpoints['sm']:
            return 'sm'
        else:
            return 'xs'

    def update_layout_for_breakpoint(self, width):
        """Update layout based on current breakpoint."""
        new_breakpoint = self.get_current_breakpoint(width)

        if new_breakpoint != self.current_breakpoint:
            self.current_breakpoint = new_breakpoint

            # Adjust spacing and margins based on breakpoint
            if new_breakpoint == 'xxl':
                self.setSpacing(32)
                self.setContentsMargins(32, 32, 32, 32)
            elif new_breakpoint == 'xl':
                self.setSpacing(28)
                self.setContentsMargins(28, 28, 28, 28)
            elif new_breakpoint == 'lg':
                self.setSpacing(24)
                self.setContentsMargins(24, 24, 24, 24)
            elif new_breakpoint == 'md':
                self.setSpacing(20)
                self.setContentsMargins(20, 20, 20, 20)
            elif new_breakpoint == 'sm':
                self.setSpacing(16)
                self.setContentsMargins(16, 16, 16, 16)
            else:  # xs
                self.setSpacing(12)
                self.setContentsMargins(12, 12, 12, 12)

            # Apply responsive configurations if any
            self._apply_responsive_configs()

    def add_responsive_widget(self, widget, configs):
        """Add a widget with responsive configuration.

        Args:
            widget: The widget to add
            configs: Dict with breakpoint-specific configurations
                    e.g., {'lg': {'row': 0, 'col': 0, 'rowspan': 1, 'colspan': 2},
                           'md': {'row': 0, 'col': 0, 'rowspan': 2, 'colspan': 1}}
        """
        self.responsive_configs[widget] = configs

        # Add widget with default configuration (lg breakpoint)
        default_config = configs.get('lg', configs.get('xl', list(configs.values())[0]))
        self.addWidget(widget,
                      default_config['row'],
                      default_config['col'],
                      default_config.get('rowspan', 1),
                      default_config.get('colspan', 1))

    def _apply_responsive_configs(self):
        """Apply responsive configurations for current breakpoint."""
        for widget, configs in self.responsive_configs.items():
            # Find the best matching configuration for current breakpoint
            config = None

            # Try current breakpoint first, then fall back to smaller ones
            breakpoint_order = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs']
            current_index = breakpoint_order.index(self.current_breakpoint)

            for i in range(current_index, len(breakpoint_order)):
                bp = breakpoint_order[i]
                if bp in configs:
                    config = configs[bp]
                    break

            if config:
                # Remove widget and re-add with new configuration
                self.removeWidget(widget)
                self.addWidget(widget,
                              config['row'],
                              config['col'],
                              config.get('rowspan', 1),
                              config.get('colspan', 1))

class ResponsiveContainer(QWidget):
    """Responsive container widget that adapts to screen size."""

    def __init__(self, parent=None):
        super().__init__(parent)

        self.responsive_layout = ResponsiveGridLayout()
        self.setLayout(self.responsive_layout)

        # Enable size tracking
        self.installEventFilter(self)

    def eventFilter(self, obj, event):
        """Handle resize events for responsive behavior."""
        if obj == self and event.type() == event.Resize:
            self.responsive_layout.update_layout_for_breakpoint(self.width())
        return super().eventFilter(obj, event)

    def add_responsive_widget(self, widget, configs):
        """Add a widget with responsive configuration."""
        self.responsive_layout.add_responsive_widget(widget, configs)

    def get_current_breakpoint(self):
        """Get the current breakpoint."""
        return self.responsive_layout.get_current_breakpoint(self.width())

class ResponsiveDesign:
    """Responsive design utilities for high-DPI and multi-resolution support."""

    @staticmethod
    def get_scale_factor():
        """Get the current display scale factor."""
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            return screen.devicePixelRatio()
        return 1.0

    @staticmethod
    def scale_size(base_size):
        """Scale a size value based on display DPI."""
        scale = ResponsiveDesign.get_scale_factor()
        return int(base_size * scale)

    @staticmethod
    def get_responsive_font_size(base_size, breakpoint='lg'):
        """Get responsive font size based on breakpoint."""
        scale_factors = {
            'xs': 0.8,
            'sm': 0.9,
            'md': 1.0,
            'lg': 1.0,
            'xl': 1.1,
            'xxl': 1.2
        }

        scale = scale_factors.get(breakpoint, 1.0)
        dpi_scale = ResponsiveDesign.get_scale_factor()

        return int(base_size * scale * dpi_scale)

    @staticmethod
    def get_responsive_spacing(base_spacing, breakpoint='lg'):
        """Get responsive spacing based on breakpoint."""
        scale_factors = {
            'xs': 0.5,
            'sm': 0.75,
            'md': 0.9,
            'lg': 1.0,
            'xl': 1.1,
            'xxl': 1.25
        }

        scale = scale_factors.get(breakpoint, 1.0)
        return int(base_spacing * scale)

class MaterialTheme:
    """Global theme manager for Material Design components."""

    @staticmethod
    def apply_global_style(app):
        """Apply global Material Design styling to the application with responsive support."""

        # Get responsive font sizes
        base_font_size = ResponsiveDesign.get_responsive_font_size(14)
        label_font_size = ResponsiveDesign.get_responsive_font_size(14)
        groupbox_font_size = ResponsiveDesign.get_responsive_font_size(16)

        # Get responsive spacing
        scrollbar_width = ResponsiveDesign.get_responsive_spacing(12)
        border_radius = ResponsiveDesign.get_responsive_spacing(8)

        global_style = f"""
            QApplication {{
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.ON_SURFACE};
                font-family: "Segoe UI", "Roboto", sans-serif;
                font-size: {base_font_size}px;
            }}

            QMainWindow {{
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.ON_SURFACE};
            }}

            QWidget {{
                background-color: transparent;
                color: {MaterialColors.ON_SURFACE};
            }}

            QLabel {{
                color: {MaterialColors.ON_SURFACE};
                font-size: {label_font_size}px;
            }}

            QGroupBox {{
                font-size: {groupbox_font_size}px;
                font-weight: 500;
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: {border_radius}px;
                margin-top: 12px;
                padding-top: 8px;
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 8px;
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.PRIMARY};
            }}

            QScrollBar:vertical {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                width: {scrollbar_width}px;
                border-radius: {scrollbar_width//2}px;
            }}

            QScrollBar::handle:vertical {{
                background-color: {MaterialColors.ON_SURFACE_VARIANT};
                border-radius: {scrollbar_width//2}px;
                min-height: 20px;
            }}

            QScrollBar::handle:vertical:hover {{
                background-color: {MaterialColors.PRIMARY};
            }}

            {MaterialTooltip.setup_global_style()}
        """

        app.setStyleSheet(global_style)

    @staticmethod
    def update_responsive_styles(widget, breakpoint):
        """Update widget styles based on current breakpoint."""
        if hasattr(widget, 'update_responsive_style'):
            widget.update_responsive_style(breakpoint)
