#!/usr/bin/env python3
"""
Test script for the new raw IR generation system.
"""

import sys
import numpy as np
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_raw_ir_generation():
    """Test the new raw IR generation system."""
    print("Testing Raw IR Generation System")
    print("=" * 50)
    
    try:
        # Import the IRExporter from the main application
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_app", "IR-Alchemist.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        # Create IRExporter
        print("1. Creating IRExporter...")
        exporter = main_module.IRExporter()
        print(f"   ✓ IRExporter created with {len(exporter.ir_reader.irs)} IRs in dataset")
        
        # Test different counts
        test_cases = [
            ("Small batch", 3),
            ("Standard batch", 10),
            ("Large batch", 15),
            ("Maximum unique", len(exporter.ir_reader.irs)),
            ("More than available", len(exporter.ir_reader.irs) + 5)
        ]
        
        for test_name, count in test_cases:
            print(f"\n2. Testing {test_name} (count={count})...")
            
            # Generate IRs
            irs = exporter.generate_multiple_irs(count=count, style="American")
            
            print(f"   ✓ Generated {len(irs)} IRs")
            
            # Verify they are raw (unprocessed)
            if irs:
                # Check that IRs are numpy arrays
                for i, ir in enumerate(irs):
                    if not isinstance(ir, np.ndarray):
                        print(f"   ✗ IR {i+1} is not a numpy array: {type(ir)}")
                        return False
                    
                    if len(ir) == 0:
                        print(f"   ✗ IR {i+1} is empty")
                        return False
                
                # Check for uniqueness in the first min(count, total_irs) IRs
                total_irs = len(exporter.ir_reader.irs)
                unique_expected = min(count, total_irs)
                
                # Compare first few IRs to check for uniqueness
                unique_count = 0
                for i in range(min(len(irs), unique_expected)):
                    is_unique = True
                    for j in range(i + 1, min(len(irs), unique_expected)):
                        if np.array_equal(irs[i], irs[j]):
                            is_unique = False
                            break
                    if is_unique:
                        unique_count += 1
                
                print(f"   ✓ Found {unique_count} unique IRs in first {unique_expected} (expected: {unique_expected})")
                
                # Check IR properties
                sample_ir = irs[0]
                print(f"   ✓ Sample IR shape: {sample_ir.shape}")
                print(f"   ✓ Sample IR range: [{sample_ir.min():.4f}, {sample_ir.max():.4f}]")
                print(f"   ✓ Sample IR RMS: {np.sqrt(np.mean(sample_ir**2)):.4f}")
                
                # Verify no post-processing artifacts
                # Raw IRs should have natural characteristics
                if np.max(np.abs(sample_ir)) > 2.0:
                    print(f"   ⚠ Sample IR has unusually high amplitude: {np.max(np.abs(sample_ir))}")
                
                # Check that IRs are different from each other (for uniqueness test)
                if len(irs) > 1 and unique_expected > 1:
                    if np.array_equal(irs[0], irs[1]) and count <= total_irs:
                        print(f"   ✗ First two IRs are identical when they should be unique")
                        return False
                    else:
                        print(f"   ✓ IRs are properly unique (when expected)")
        
        # Test style parameter (should be ignored)
        print(f"\n3. Testing style parameter (should be ignored)...")
        styles = ["American", "British", "German", "Random", "NonExistent"]
        
        for style in styles:
            irs = exporter.generate_multiple_irs(count=3, style=style)
            print(f"   ✓ Style '{style}': Generated {len(irs)} IRs")
        
        # Test that we get raw IRs (no processing)
        print(f"\n4. Verifying IRs are raw (unprocessed)...")
        
        # Get a few IRs
        test_irs = exporter.generate_multiple_irs(count=5, style="Random")
        
        # Check that they match original dataset IRs
        dataset_irs = exporter.ir_reader.irs
        matches_found = 0
        
        for test_ir in test_irs:
            for dataset_ir in dataset_irs:
                if np.array_equal(test_ir, dataset_ir):
                    matches_found += 1
                    break
        
        print(f"   ✓ {matches_found}/{len(test_irs)} generated IRs match original dataset IRs")
        
        if matches_found == len(test_irs):
            print(f"   ✓ All generated IRs are raw (unprocessed) from dataset")
        else:
            print(f"   ⚠ Some IRs may have been processed or modified")
        
        print(f"\n🎉 All tests passed! Raw IR generation system is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """Test performance of raw IR generation."""
    print(f"\n" + "=" * 50)
    print("Performance Test")
    print("=" * 50)
    
    try:
        import time
        import importlib.util
        
        spec = importlib.util.spec_from_file_location("main_app", "IR-Alchemist.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        exporter = main_module.IRExporter()
        
        # Test generation speed
        counts = [1, 5, 10, 20, 50]
        
        for count in counts:
            start_time = time.time()
            irs = exporter.generate_multiple_irs(count=count)
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"Generated {len(irs)} IRs in {duration:.4f} seconds ({duration/len(irs)*1000:.2f} ms per IR)")
        
        print(f"✅ Performance test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def main():
    """Main test function."""
    print("Raw IR Generation Test Suite")
    print("=" * 60)
    
    success = True
    
    # Test raw IR generation
    if not test_raw_ir_generation():
        success = False
    
    # Test performance
    if not test_performance():
        success = False
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎉 All tests passed!")
        print("Raw IR generation system is working correctly:")
        print("  ✓ Randomly selects IRs from dataset")
        print("  ✓ Ensures uniqueness within batches")
        print("  ✓ Returns unprocessed, raw IRs")
        print("  ✓ Ignores style parameter")
        print("  ✓ Maintains interface compatibility")
        print("  ✓ Good performance characteristics")
    else:
        print("❌ Some tests failed!")
        print("Please check the errors above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
