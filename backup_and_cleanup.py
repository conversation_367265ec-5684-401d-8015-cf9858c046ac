#!/usr/bin/env python3
"""
Backup and Cleanup Script for IR-Alchemist
This script backs up the original IR directory and optionally removes it after verification.
"""

import os
import sys
import shutil
import zipfile
import logging
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_backup(source_dir, backup_file):
    """
    Create a ZIP backup of the source directory.
    
    Args:
        source_dir: Directory to backup
        backup_file: Output ZIP file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logging.info(f"Creating backup: {source_dir} -> {backup_file}")
        
        with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            source_path = Path(source_dir)
            
            for file_path in source_path.rglob('*'):
                if file_path.is_file():
                    # Calculate relative path for the archive
                    arcname = file_path.relative_to(source_path.parent)
                    zipf.write(file_path, arcname)
                    
                    # Log progress for every 20 files
                    if len(zipf.namelist()) % 20 == 0:
                        logging.info(f"  Backed up {len(zipf.namelist())} files...")
        
        # Verify backup
        backup_size = os.path.getsize(backup_file)
        with zipfile.ZipFile(backup_file, 'r') as zipf:
            file_count = len(zipf.namelist())
        
        logging.info(f"Backup created successfully:")
        logging.info(f"  File: {backup_file}")
        logging.info(f"  Size: {backup_size:,} bytes ({backup_size/1024/1024:.1f} MB)")
        logging.info(f"  Files: {file_count}")
        
        return True
        
    except Exception as e:
        logging.error(f"Error creating backup: {e}")
        return False

def verify_packaged_dataset():
    """
    Verify that the packaged dataset is working correctly.
    
    Returns:
        True if verification successful, False otherwise
    """
    try:
        logging.info("Verifying packaged dataset...")
        
        # Check if package file exists
        package_file = "ir_dataset.irpkg"
        if not os.path.exists(package_file):
            logging.error(f"Package file not found: {package_file}")
            return False
        
        # Test loading the package
        from irpkg_reader import IRPKGReader
        reader = IRPKGReader()
        
        if len(reader.irs) == 0:
            logging.error("No IRs loaded from package")
            return False
        
        # Test IR generation
        from test_ir_exporter import IRExporter
        exporter = IRExporter(ir_dir=None)
        
        test_irs = exporter.generate_multiple_irs(count=1, style="Random")
        if not test_irs or len(test_irs) == 0:
            logging.error("Failed to generate test IRs")
            return False
        
        logging.info(f"✓ Package verification successful:")
        logging.info(f"  Loaded {len(reader.irs)} IRs from package")
        logging.info(f"  Generated test IR successfully")
        
        return True
        
    except Exception as e:
        logging.error(f"Package verification failed: {e}")
        return False

def remove_original_directory(source_dir):
    """
    Remove the original IR directory.
    
    Args:
        source_dir: Directory to remove
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logging.info(f"Removing original directory: {source_dir}")
        
        if not os.path.exists(source_dir):
            logging.warning(f"Directory does not exist: {source_dir}")
            return True
        
        # Count files before removal
        file_count = sum(1 for _ in Path(source_dir).rglob('*') if _.is_file())
        dir_size = sum(f.stat().st_size for f in Path(source_dir).rglob('*') if f.is_file())
        
        # Remove the directory
        shutil.rmtree(source_dir)
        
        logging.info(f"✓ Directory removed successfully:")
        logging.info(f"  Removed {file_count} files")
        logging.info(f"  Freed {dir_size:,} bytes ({dir_size/1024/1024:.1f} MB)")
        
        return True
        
    except Exception as e:
        logging.error(f"Error removing directory: {e}")
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("IR-Alchemist Backup and Cleanup")
    print("=" * 60)
    
    # Configuration
    source_dir = "E:\\IRs\\IR-Alchemist IRs"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"IR-Alchemist_IRs_backup_{timestamp}.zip"
    
    print(f"Source directory: {source_dir}")
    print(f"Backup file: {backup_file}")
    print()
    
    # Check if source directory exists
    if not os.path.exists(source_dir):
        print(f"❌ Source directory not found: {source_dir}")
        print("The directory may have already been removed or moved.")
        return 0
    
    # Verify packaged dataset first
    print("🔍 Verifying packaged dataset...")
    if not verify_packaged_dataset():
        print("❌ Packaged dataset verification failed!")
        print("Please ensure the packaged dataset is working before proceeding.")
        return 1
    
    print("✅ Packaged dataset verification successful!")
    print()
    
    # Create backup
    print("💾 Creating backup of original IR directory...")
    if not create_backup(source_dir, backup_file):
        print("❌ Backup creation failed!")
        return 1
    
    print("✅ Backup created successfully!")
    print()
    
    # Ask user if they want to remove the original directory
    print("⚠️  The original IR directory can now be safely removed.")
    print("The packaged dataset contains all the IR data in an encrypted format.")
    print()
    response = input("Do you want to remove the original IR directory? (y/N): ")
    
    if response.lower() == 'y':
        print()
        print("🗑️  Removing original IR directory...")
        if remove_original_directory(source_dir):
            print("✅ Original directory removed successfully!")
            print()
            print("📋 Summary:")
            print(f"  ✓ Backup created: {backup_file}")
            print(f"  ✓ Original directory removed: {source_dir}")
            print(f"  ✓ Packaged dataset: ir_dataset.irpkg")
            print()
            print("🎉 Cleanup completed successfully!")
            print("Your IR data is now protected in the encrypted package.")
        else:
            print("❌ Failed to remove original directory!")
            return 1
    else:
        print()
        print("📋 Summary:")
        print(f"  ✓ Backup created: {backup_file}")
        print(f"  ✓ Original directory preserved: {source_dir}")
        print(f"  ✓ Packaged dataset: ir_dataset.irpkg")
        print()
        print("ℹ️  You can manually remove the original directory later if desired.")
        print("The backup and packaged dataset provide full protection of your IR data.")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
