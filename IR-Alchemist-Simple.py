#!/usr/bin/env python3
"""
IR-Alchemist - Dataset Version (Simplified)
A simplified version that uses the IR dataset instead of ML models.
"""

import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

# Import our IR reader and exporter
from test_ir_exporter import IRExporter

class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, exporter, style, count, seed=None):
        super().__init__()
        self.exporter = exporter
        self.style = style
        self.count = count
        self.seed = seed

    @pyqtSlot()
    def run(self):
        try:
            if self.seed is not None:
                np.random.seed(self.seed)
            logging.info("Starting IR selection with style=%s, count=%d", self.style, self.count)
            irs = self.exporter.generate_multiple_irs(count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            logging.error("Error during IR selection: %s", str(e))
            self.error.emit(str(e))

class IRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist - Dataset Version")
        self.setGeometry(100, 100, 1000, 700)
        self.last_generated_irs = None
        self.selected_style = "American"
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.current_temp_file = None

        # Initialize IR exporter
        try:
            self.exporter = IRExporter()
            logging.info(f"Loaded {len(self.exporter.ir_reader.irs)} IRs from dataset")
        except Exception as e:
            logging.error(f"Failed to initialize IR exporter: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load IR dataset: {e}")
            sys.exit(1)

        self.init_ui()

    def init_ui(self):
        # Create central widget
        central_widget = QWidget()
        main_layout = QVBoxLayout()

        # Title
        title = QLabel("IR-Alchemist")
        title.setStyleSheet("font-size: 32px; font-weight: bold; color: #007ACC; margin: 20px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # Subtitle
        subtitle = QLabel("Dataset-Based Impulse Response Generator")
        subtitle.setStyleSheet("font-size: 16px; color: #666666; margin-bottom: 20px;")
        subtitle.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(subtitle)

        # Style selection
        style_group = QGroupBox("Cabinet Style")
        style_layout = QHBoxLayout()
        
        self.style_buttons = QButtonGroup()
        
        for style in ["American", "British", "German", "Random"]:
            btn = QRadioButton(style)
            if style == "American":
                btn.setChecked(True)
            btn.clicked.connect(lambda checked, s=style: self.update_style(s))
            self.style_buttons.addButton(btn)
            style_layout.addWidget(btn)
        
        style_group.setLayout(style_layout)
        main_layout.addWidget(style_group)

        # Generate button
        self.generate_round_btn = QPushButton("Generate 10 IRs")
        self.generate_round_btn.setFixedHeight(60)
        self.generate_round_btn.setStyleSheet("""
            QPushButton {
                background-color: #007ACC;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.generate_round_btn.clicked.connect(self.update_irs)
        main_layout.addWidget(self.generate_round_btn)

        # Status label
        self.status_label = QLabel(f"Ready - Loaded {len(self.exporter.ir_reader.irs)} IRs from dataset")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("margin: 10px; padding: 10px; background-color: #f0f0f0; border-radius: 4px;")
        main_layout.addWidget(self.status_label)

        # IR list area
        self.ir_list_widget = QWidget()
        self.ir_list_layout = QVBoxLayout()
        self.ir_list_widget.setLayout(self.ir_list_layout)
        main_layout.addWidget(self.ir_list_widget)

        # Export controls
        export_group = QGroupBox("Export Options")
        export_layout = QHBoxLayout()
        
        self.export_all_btn = QPushButton("Export All IRs")
        self.export_all_btn.clicked.connect(self.export_all_irs)
        self.export_all_btn.setEnabled(False)
        export_layout.addWidget(self.export_all_btn)
        
        export_group.setLayout(export_layout)
        main_layout.addWidget(export_group)

        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def update_style(self, style):
        if self.sender().isChecked():
            self.selected_style = style
            self.status_label.setText(f"Selected style: {style}")

    def update_irs(self):
        seed = None
        self.generate_round_btn.setEnabled(False)
        self.status_label.setText("Generating IRs...")
        
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(exporter=self.exporter, style=self.selected_style, count=10, seed=seed)
        self.worker.moveToThread(self.worker_thread)
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        self.worker_thread.start()

    def on_generation_finished(self, irs):
        self.last_generated_irs = irs
        self.generate_round_btn.setEnabled(True)
        self.export_all_btn.setEnabled(True)
        self.status_label.setText(f"Generated {len(irs)} IRs for {self.selected_style} style")
        
        # Clear previous IR widgets
        for i in reversed(range(self.ir_list_layout.count())):
            self.ir_list_layout.itemAt(i).widget().setParent(None)
        
        # Create simple IR display widgets
        for i, ir in enumerate(irs):
            ir_widget = QWidget()
            ir_layout = QHBoxLayout()
            
            # IR info
            info_label = QLabel(f"IR {i+1}: {len(ir)} samples, Peak: {np.max(np.abs(ir)):.4f}")
            ir_layout.addWidget(info_label)
            
            # Play button
            play_btn = QPushButton("Play")
            play_btn.clicked.connect(lambda checked, idx=i: self.play_ir(idx))
            ir_layout.addWidget(play_btn)
            
            # Export button
            export_btn = QPushButton("Export")
            export_btn.clicked.connect(lambda checked, idx=i: self.export_single_ir(idx))
            ir_layout.addWidget(export_btn)
            
            ir_widget.setLayout(ir_layout)
            ir_widget.setStyleSheet("border: 1px solid #cccccc; margin: 2px; padding: 5px; border-radius: 4px;")
            self.ir_list_layout.addWidget(ir_widget)

    def on_generation_error(self, error_msg):
        self.generate_round_btn.setEnabled(True)
        self.status_label.setText(f"Error: {error_msg}")
        QMessageBox.critical(self, "Generation Error", f"Failed to generate IRs: {error_msg}")

    def play_ir(self, index):
        if self.last_generated_irs and 0 <= index < len(self.last_generated_irs):
            ir = self.last_generated_irs[index]
            try:
                # Create temporary file
                if self.current_temp_file:
                    try:
                        os.unlink(self.current_temp_file)
                    except:
                        pass
                
                temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
                self.current_temp_file = temp_file.name
                temp_file.close()
                
                # Write IR to file
                sf.write(self.current_temp_file, ir, 48000, subtype='PCM_24')
                
                # Play the file
                self.player.setMedia(QMediaContent(QUrl.fromLocalFile(self.current_temp_file)))
                self.player.play()
                
                self.status_label.setText(f"Playing IR {index + 1}")
                
            except Exception as e:
                QMessageBox.critical(self, "Playback Error", f"Failed to play IR: {e}")

    def export_single_ir(self, index):
        if self.last_generated_irs and 0 <= index < len(self.last_generated_irs):
            ir = self.last_generated_irs[index]
            filename, _ = QFileDialog.getSaveFileName(self, f"Export IR {index + 1}", f"ir_{index + 1}.wav", "WAV files (*.wav)")
            if filename:
                try:
                    sf.write(filename, ir, 48000, subtype='PCM_24')
                    self.status_label.setText(f"Exported IR {index + 1} to {filename}")
                except Exception as e:
                    QMessageBox.critical(self, "Export Error", f"Failed to export IR: {e}")

    def export_all_irs(self):
        if not self.last_generated_irs:
            return
        
        folder = QFileDialog.getExistingDirectory(self, "Select Export Folder")
        if folder:
            try:
                for i, ir in enumerate(self.last_generated_irs):
                    filename = os.path.join(folder, f"ir_{i + 1:02d}.wav")
                    sf.write(filename, ir, 48000, subtype='PCM_24')
                
                self.status_label.setText(f"Exported {len(self.last_generated_irs)} IRs to {folder}")
                QMessageBox.information(self, "Export Complete", f"Successfully exported {len(self.last_generated_irs)} IRs to {folder}")
                
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export IRs: {e}")

    def closeEvent(self, event):
        # Clean up temporary files
        if self.current_temp_file:
            try:
                os.unlink(self.current_temp_file)
            except:
                pass
        event.accept()

def main():
    try:
        app = QApplication(sys.argv)
        
        # Load font
        font_path = os.path.join(os.path.dirname(__file__), "Exo-VariableFont_wght.ttf")
        if os.path.exists(font_path):
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id != -1:
                families = QFontDatabase.applicationFontFamilies(font_id)
                if families:
                    default_font = QFont(families[0])
                    app.setFont(default_font)
                    print("Loaded font:", families[0])
        
        print("Creating IR-Alchemist GUI...")
        window = IRGeneratorGUI()
        print("Showing window...")
        window.show()
        print("Starting application...")
        
        return app.exec_()
        
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
