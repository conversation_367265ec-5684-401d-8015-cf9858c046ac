import os
import struct
import json
import zlib
import numpy as np
import logging
import random
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
import base64

class IRPKGReader:
    """
    Reader for packaged IR (Impulse Response) dataset files.
    This class loads IR files from an encrypted, compressed package file.
    """

    def __init__(self, package_file=None, password="IR-Alchemist-2024-Dataset"):
        """
        Initialize the IR reader.

        Args:
            package_file (str): Path to the packaged dataset file. If None, looks for ir_dataset.irpkg
            password (str): Password for decryption
        """
        if package_file is None:
            package_file = os.path.join(os.path.dirname(__file__), "ir_dataset.irpkg")

        self.package_file = package_file
        self.password = password.encode('utf-8')
        self.salt = b"IR-Alchemist-Salt-v2"

        self.irs = []
        self.styles = ["American", "British", "German", "Random"]
        self.ir_length = 2048
        self.sample_rate = 48000

        # Map styles to IR indices for better organization
        self.style_to_irs = {
            "American": [],
            "British": [],
            "German": [],
            "Random": []
        }

        # Load the IRs from the package
        self._load_package()
    
    def _derive_key(self):
        """Derive encryption key from password."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return key

    def _decrypt_data(self, encrypted_data):
        """Decrypt data using Fernet symmetric encryption."""
        try:
            key = self._derive_key()
            f = Fernet(key)
            return f.decrypt(encrypted_data)
        except Exception as e:
            logging.error(f"Error decrypting data: {e}")
            return None

    def _load_package(self):
        """Load IR data from the packaged dataset file."""
        try:
            # Check if package file exists
            if not os.path.exists(self.package_file):
                logging.warning(f"Package file {self.package_file} does not exist")
                self._create_dummy_irs()
                return

            logging.info(f"Loading IR package from {self.package_file}")

            with open(self.package_file, 'rb') as f:
                # Read and validate header
                header_data = f.read(16)
                if len(header_data) != 16:
                    raise ValueError("Invalid package file: incomplete header")

                magic, version, data_size, checksum = struct.unpack('<6sHII', header_data)

                if magic != b'IRALCH':
                    raise ValueError("Invalid package file: wrong magic header")

                if version != 2:
                    raise ValueError(f"Unsupported package version: {version}")

                # Read encrypted data
                encrypted_data = f.read(data_size)
                if len(encrypted_data) != data_size:
                    raise ValueError("Invalid package file: incomplete data")

                # Verify checksum
                if zlib.crc32(encrypted_data) != checksum:
                    raise ValueError("Invalid package file: checksum mismatch")

            # Decrypt the data
            logging.info("Decrypting package data...")
            compressed_data = self._decrypt_data(encrypted_data)
            if compressed_data is None:
                raise ValueError("Failed to decrypt package data")

            # Decompress the data
            logging.info("Decompressing package data...")
            json_data = zlib.decompress(compressed_data)

            # Parse JSON
            logging.info("Parsing package data...")
            dataset = json.loads(json_data.decode('utf-8'))

            # Validate dataset structure
            if 'metadata' not in dataset or 'irs' not in dataset:
                raise ValueError("Invalid dataset structure")

            metadata = dataset['metadata']
            self.sample_rate = metadata.get('sample_rate', 48000)
            self.ir_length = metadata.get('ir_length', 2048)

            # Load IRs from dataset
            total_loaded = 0
            for style in self.styles:
                if style not in dataset['irs']:
                    continue

                style_irs = dataset['irs'][style]
                style_indices = []

                for ir_entry in style_irs:
                    try:
                        # Decode base64 and convert back to numpy array
                        ir_bytes = base64.b64decode(ir_entry['data'].encode('ascii'))
                        ir_data = np.frombuffer(ir_bytes, dtype=np.float32)

                        # Validate IR length
                        if len(ir_data) != self.ir_length:
                            logging.warning(f"IR length mismatch: expected {self.ir_length}, got {len(ir_data)}")
                            continue

                        # Add to main IR list
                        ir_index = len(self.irs)
                        self.irs.append(ir_data)
                        style_indices.append(ir_index)
                        total_loaded += 1

                    except Exception as e:
                        logging.error(f"Error loading IR from package: {e}")
                        continue

                self.style_to_irs[style] = style_indices
                logging.info(f"Loaded {len(style_indices)} {style} IRs")

            if total_loaded == 0:
                logging.warning("No IRs loaded from package, creating dummy IRs")
                self._create_dummy_irs()
            else:
                logging.info(f"Successfully loaded {total_loaded} IRs from package")

        except Exception as e:
            logging.error(f"Error loading package: {e}")
            self._create_dummy_irs()
    
    def _distribute_irs_to_styles(self):
        """Distribute loaded IRs among different styles (fallback for dummy IRs)."""
        # Shuffle the IRs for random distribution
        shuffled_indices = list(range(len(self.irs)))
        random.shuffle(shuffled_indices)

        # Distribute IRs evenly among styles
        irs_per_style = len(self.irs) // len(self.styles)
        remainder = len(self.irs) % len(self.styles)

        start_idx = 0
        for i, style in enumerate(self.styles):
            # Give extra IRs to the first few styles if there's a remainder
            count = irs_per_style + (1 if i < remainder else 0)
            end_idx = start_idx + count

            self.style_to_irs[style] = shuffled_indices[start_idx:end_idx]
            start_idx = end_idx
    
    def _create_dummy_irs(self):
        """Create dummy IRs if loading fails."""
        logging.warning("Creating dummy IRs as fallback")
        # Create 40 dummy IRs (10 for each style)
        for _ in range(40):
            # Create a simple impulse response (exponential decay)
            ir = np.zeros(self.ir_length)
            ir[0] = 1.0
            decay = np.exp(-np.arange(self.ir_length) / (self.ir_length / 10))
            ir = ir * decay
            
            # Add some random variations
            ir += np.random.normal(0, 0.01, self.ir_length)
            
            # Normalize
            ir = ir / np.max(np.abs(ir))
            
            self.irs.append(ir)
        
        # Distribute dummy IRs to styles
        self._distribute_irs_to_styles()
    
    def get_irs_by_style(self, style, count=10):
        """
        Get a specified number of IRs for a given style.
        
        Args:
            style (str): The style of IRs to get ("American", "British", "German", or "Random")
            count (int): The number of IRs to return
            
        Returns:
            list: A list of numpy arrays containing the IRs
        """
        if style not in self.styles:
            style = "American"
        
        # Get IRs for the specified style
        style_indices = self.style_to_irs[style]
        
        if not style_indices:
            # If no IRs for this style, use all IRs
            style_indices = list(range(len(self.irs)))
        
        # Select random IRs from the style
        selected_count = min(count, len(style_indices))
        selected_indices = random.sample(style_indices, selected_count)
        
        # If we need more IRs than available, repeat some
        while len(selected_indices) < count and style_indices:
            additional_needed = count - len(selected_indices)
            additional_indices = random.choices(style_indices, k=additional_needed)
            selected_indices.extend(additional_indices)
        
        selected_irs = [self.irs[i] for i in selected_indices[:count]]
        
        return selected_irs

# Test the reader
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    reader = IRPKGReader()
    for style in reader.styles:
        irs = reader.get_irs_by_style(style, 2)
        print(f"Got {len(irs)} IRs for style {style}")
        for i, ir in enumerate(irs):
            print(f"  IR {i+1}: shape={ir.shape}, min={ir.min():.4f}, max={ir.max():.4f}")
