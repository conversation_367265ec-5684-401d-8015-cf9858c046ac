#!/usr/bin/env python3
"""
Test script for the native PyQt5 visualization system.
"""

import sys
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt
from native_visualization import (
    FrequencyResponsePlot, WaveformPlot, SpectrogramPlot, 
    IRAnalysisPanel, NativeDetailCanvas
)

class VisualizationTestWindow(QMainWindow):
    """Test window for visualization components."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Native Visualization Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # Create test buttons
        button_layout = QHBoxLayout()
        
        self.test_sine_btn = QPushButton("Test Sine Wave")
        self.test_sine_btn.clicked.connect(self.test_sine_wave)
        button_layout.addWidget(self.test_sine_btn)
        
        self.test_impulse_btn = QPushButton("Test Impulse")
        self.test_impulse_btn.clicked.connect(self.test_impulse)
        button_layout.addWidget(self.test_impulse_btn)
        
        self.test_noise_btn = QPushButton("Test Noise")
        self.test_noise_btn.clicked.connect(self.test_noise)
        button_layout.addWidget(self.test_noise_btn)
        
        self.test_ir_btn = QPushButton("Test Real IR")
        self.test_ir_btn.clicked.connect(self.test_real_ir)
        button_layout.addWidget(self.test_ir_btn)
        
        layout.addLayout(button_layout)
        
        # Create the native detail canvas
        self.detail_canvas = NativeDetailCanvas()
        layout.addWidget(self.detail_canvas)
        
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
        
        # Test with initial data
        self.test_sine_wave()
    
    def test_sine_wave(self):
        """Test with a sine wave."""
        print("Testing sine wave...")
        sample_rate = 48000
        duration = 0.1  # 100ms
        frequency = 1000  # 1kHz
        
        t = np.linspace(0, duration, int(sample_rate * duration))
        ir = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 20)  # Decaying sine
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_impulse(self):
        """Test with an impulse response."""
        print("Testing impulse...")
        sample_rate = 48000
        length = 2048
        
        # Create impulse with exponential decay
        ir = np.zeros(length)
        ir[0] = 1.0
        decay = np.exp(-np.arange(length) / (length / 10))
        ir = ir * decay
        
        # Add some resonances
        for freq in [200, 800, 2000]:
            t = np.arange(length) / sample_rate
            resonance = 0.3 * np.sin(2 * np.pi * freq * t) * np.exp(-t * 5)
            ir += resonance
        
        # Normalize
        ir = ir / np.max(np.abs(ir))
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_noise(self):
        """Test with filtered noise."""
        print("Testing filtered noise...")
        sample_rate = 48000
        length = 2048
        
        # Create filtered noise
        noise = np.random.normal(0, 1, length)
        
        # Apply simple low-pass filter (moving average)
        window_size = 10
        ir = np.convolve(noise, np.ones(window_size)/window_size, mode='same')
        
        # Apply exponential decay
        decay = np.exp(-np.arange(length) / (length / 8))
        ir = ir * decay
        
        # Normalize
        ir = ir / np.max(np.abs(ir))
        
        self.detail_canvas.update_detail(ir, sample_rate)
    
    def test_real_ir(self):
        """Test with a realistic IR from the dataset."""
        print("Testing real IR from dataset...")
        try:
            from irpkg_reader import IRPKGReader
            reader = IRPKGReader()
            
            if reader.irs:
                ir = reader.irs[0]  # Use first IR
                self.detail_canvas.update_detail(ir, 48000)
                print(f"Loaded real IR with {len(ir)} samples")
            else:
                print("No IRs available in dataset")
                self.test_impulse()  # Fallback
                
        except Exception as e:
            print(f"Error loading real IR: {e}")
            self.test_impulse()  # Fallback

def test_individual_components():
    """Test individual visualization components."""
    print("Testing individual components...")
    
    app = QApplication(sys.argv)
    
    # Test data
    sample_rate = 48000
    length = 2048
    t = np.arange(length) / sample_rate
    
    # Create test IR
    ir = np.exp(-t * 10) * np.sin(2 * np.pi * 1000 * t)
    ir += 0.5 * np.exp(-t * 5) * np.sin(2 * np.pi * 500 * t)
    ir = ir / np.max(np.abs(ir))
    
    # Test frequency response plot
    print("1. Testing FrequencyResponsePlot...")
    freq_plot = FrequencyResponsePlot()
    freqs = np.fft.rfftfreq(length, 1/sample_rate)
    fft = np.fft.rfft(ir)
    mag_db = 20 * np.log10(np.abs(fft) + 1e-9)
    freq_plot.set_data(freqs, mag_db)
    freq_plot.show()
    
    # Test waveform plot
    print("2. Testing WaveformPlot...")
    wave_plot = WaveformPlot()
    wave_plot.set_data(ir, sample_rate)
    wave_plot.show()
    
    # Test spectrogram plot
    print("3. Testing SpectrogramPlot...")
    spec_plot = SpectrogramPlot()
    spec_plot.set_data(ir, sample_rate)
    spec_plot.show()
    
    # Test analysis panel
    print("4. Testing IRAnalysisPanel...")
    analysis_panel = IRAnalysisPanel()
    analysis_panel.update_analysis(ir, sample_rate)
    analysis_panel.show()
    
    print("✓ All individual components created successfully!")
    
    return app

def main():
    """Main test function."""
    print("Native PyQt5 Visualization Test")
    print("=" * 40)
    
    # Test individual components first
    app = test_individual_components()
    
    # Test complete detail canvas
    print("\n5. Testing complete NativeDetailCanvas...")
    test_window = VisualizationTestWindow()
    test_window.show()
    
    print("\n✅ All visualization tests completed successfully!")
    print("\nTest window controls:")
    print("- Sine Wave: Test with a decaying sine wave")
    print("- Impulse: Test with a realistic impulse response")
    print("- Noise: Test with filtered noise")
    print("- Real IR: Test with actual IR from dataset")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
