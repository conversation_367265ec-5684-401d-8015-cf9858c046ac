#!/usr/bin/env python3
"""
Comprehensive test for stereo audio integration and modern UI features.
"""

import sys
import os
import numpy as np
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_stereo_audio_handler():
    """Test the stereo audio handler functionality."""
    print("Testing Stereo Audio Handler")
    print("=" * 50)
    
    try:
        from stereo_audio_handler import stereo_handler
        
        # Test 1: Load Sample.wav
        print("1. Testing Sample.wav loading...")
        sample_path = "Sample.wav"
        if not os.path.exists(sample_path):
            print(f"   ✗ Sample.wav not found at {sample_path}")
            return False
        
        audio_data, sample_rate, channels = stereo_handler.load_audio(sample_path)
        print(f"   ✓ Loaded: {audio_data.shape}, {sample_rate}Hz, {channels} channels")
        
        # Test 2: Channel separation
        print("2. Testing channel separation...")
        if channels == 2:
            left, right = stereo_handler.separate_channels(audio_data)
            print(f"   ✓ Left channel: {left.shape}, RMS: {np.sqrt(np.mean(left**2)):.6f}")
            print(f"   ✓ Right channel: {right.shape}, RMS: {np.sqrt(np.mean(right**2)):.6f}")
        else:
            print(f"   ⚠ Sample is mono, testing mono-to-stereo conversion...")
            stereo_audio = stereo_handler.ensure_stereo(audio_data)
            print(f"   ✓ Converted to stereo: {stereo_audio.shape}")
        
        # Test 3: Stereo convolution with mono IR
        print("3. Testing stereo convolution...")
        # Create a simple test IR
        test_ir = np.array([1.0, 0.5, 0.25, 0.125, 0.0625])
        
        # Test different convolution modes
        modes = ["stereo", "mono", "left_only", "right_only"]
        for mode in modes:
            try:
                result = stereo_handler.stereo_convolution(audio_data, test_ir, mode=mode)
                result_channels = 1 if len(result.shape) == 1 else result.shape[1]
                print(f"   ✓ Mode '{mode}': Output {result.shape}, {result_channels} channels")
            except Exception as e:
                print(f"   ✗ Mode '{mode}' failed: {e}")
                return False
        
        # Test 4: Audio info
        print("4. Testing audio info extraction...")
        info = stereo_handler.get_audio_info(audio_data, sample_rate)
        print(f"   ✓ Duration: {info['duration']:.3f}s")
        print(f"   ✓ RMS: {info['rms']:.6f}")
        if 'stereo_correlation' in info:
            print(f"   ✓ Stereo correlation: {info['stereo_correlation']:.3f}")
        
        # Test 5: Normalization
        print("5. Testing audio normalization...")
        normalized = stereo_handler.normalize_audio(audio_data, target_level=0.5)
        norm_peak = np.max(np.abs(normalized))
        print(f"   ✓ Normalized peak: {norm_peak:.3f} (target: 0.5)")
        
        print("✅ All stereo audio handler tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Stereo audio handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_modern_ui_components():
    """Test the modern UI framework components."""
    print("\n" + "=" * 50)
    print("Testing Modern UI Components")
    print("=" * 50)
    
    try:
        from modern_ui_framework import (
            MaterialTheme, MaterialButton, MaterialCard, MaterialTextField,
            MaterialProgressBar, MaterialSwitch, MaterialColors
        )
        
        app = QApplication(sys.argv)
        
        # Test 1: Material Theme
        print("1. Testing Material Theme...")
        MaterialTheme.apply_global_style(app)
        print("   ✓ Material theme applied successfully")
        
        # Test 2: Material Components
        print("2. Testing Material Components...")
        
        # Create test window
        window = QMainWindow()
        window.setWindowTitle("Modern UI Test")
        window.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # Test MaterialButton
        filled_btn = MaterialButton("Filled Button", "filled")
        outlined_btn = MaterialButton("Outlined Button", "outlined")
        text_btn = MaterialButton("Text Button", "text")
        
        layout.addWidget(QLabel("Material Buttons:"))
        layout.addWidget(filled_btn)
        layout.addWidget(outlined_btn)
        layout.addWidget(text_btn)
        
        # Test MaterialCard
        card = MaterialCard(elevation=2)
        card_layout = QVBoxLayout()
        card_layout.addWidget(QLabel("This is a Material Card"))
        card.setLayout(card_layout)
        layout.addWidget(card)
        
        # Test MaterialTextField
        text_field = MaterialTextField("Enter text here...")
        layout.addWidget(text_field)
        
        # Test MaterialProgressBar
        progress = MaterialProgressBar()
        progress.setValue(75)
        layout.addWidget(progress)
        
        # Test MaterialSwitch
        switch = MaterialSwitch()
        layout.addWidget(switch)
        
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        print("   ✓ All Material components created successfully")
        
        # Test 3: Color Palette
        print("3. Testing Material Color Palette...")
        colors = [
            MaterialColors.PRIMARY,
            MaterialColors.SECONDARY,
            MaterialColors.SURFACE,
            MaterialColors.BACKGROUND,
            MaterialColors.ON_SURFACE
        ]
        
        for color in colors:
            # Validate color format
            if not (color.startswith('#') and len(color) == 7):
                print(f"   ✗ Invalid color format: {color}")
                return False
        
        print("   ✓ All colors have valid format")
        
        print("✅ All modern UI component tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Modern UI component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_visualization():
    """Test the enhanced native visualization with stereo support."""
    print("\n" + "=" * 50)
    print("Testing Enhanced Visualization")
    print("=" * 50)
    
    try:
        from native_visualization import NativeDetailCanvas, WaveformPlot
        from stereo_audio_handler import stereo_handler
        
        app = QApplication.instance() or QApplication(sys.argv)
        
        # Test 1: Load stereo audio
        print("1. Testing stereo audio loading...")
        if os.path.exists("Sample.wav"):
            audio_data, sample_rate, channels = stereo_handler.load_audio("Sample.wav")
            print(f"   ✓ Loaded stereo audio: {channels} channels")
        else:
            # Create synthetic stereo audio for testing
            duration = 1.0  # 1 second
            samples = int(duration * 48000)
            t = np.linspace(0, duration, samples)
            
            # Create different signals for left and right channels
            left = np.sin(2 * np.pi * 440 * t) * np.exp(-t * 2)  # 440 Hz decaying sine
            right = np.sin(2 * np.pi * 880 * t) * np.exp(-t * 3)  # 880 Hz decaying sine
            
            audio_data = np.column_stack([left, right])
            sample_rate = 48000
            channels = 2
            print("   ✓ Created synthetic stereo audio for testing")
        
        # Test 2: Enhanced WaveformPlot
        print("2. Testing enhanced WaveformPlot...")
        waveform_plot = WaveformPlot()
        waveform_plot.set_data(audio_data, sample_rate)
        
        # Test different channel display modes
        modes = ["both", "left", "right", "combined"]
        for mode in modes:
            waveform_plot.set_channel_display(mode)
            print(f"   ✓ Channel display mode '{mode}' set successfully")
        
        # Test 3: NativeDetailCanvas with stereo
        print("3. Testing NativeDetailCanvas with stereo...")
        detail_canvas = NativeDetailCanvas()
        
        # Test with stereo audio
        detail_canvas.update_detail(audio_data, sample_rate)
        print("   ✓ DetailCanvas updated with stereo audio")
        
        # Test with mono audio (converted from stereo)
        mono_audio = stereo_handler.ensure_mono(audio_data)
        detail_canvas.update_detail(mono_audio, sample_rate)
        print("   ✓ DetailCanvas updated with mono audio")
        
        print("✅ All enhanced visualization tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced visualization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_application_integration():
    """Test the main application with stereo integration."""
    print("\n" + "=" * 50)
    print("Testing Main Application Integration")
    print("=" * 50)
    
    try:
        # Test that the main application can be imported
        print("1. Testing main application import...")
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_app", "IR-Alchemist.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        print("   ✓ Main application imported successfully")
        
        # Test IRExporter with stereo support
        print("2. Testing IRExporter...")
        exporter = main_module.IRExporter()
        print(f"   ✓ IRExporter created with {len(exporter.ir_reader.irs)} IRs")
        
        # Generate some IRs
        irs = exporter.generate_multiple_irs(count=3)
        print(f"   ✓ Generated {len(irs)} IRs successfully")
        
        print("✅ Main application integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Main application integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("Stereo Integration and Modern UI Test Suite")
    print("=" * 70)
    
    success = True
    
    # Test stereo audio handler
    if not test_stereo_audio_handler():
        success = False
    
    # Test modern UI components
    if not test_modern_ui_components():
        success = False
    
    # Test enhanced visualization
    if not test_enhanced_visualization():
        success = False
    
    # Test main application integration
    if not test_main_application_integration():
        success = False
    
    print(f"\n" + "=" * 70)
    if success:
        print("🎉 All tests passed!")
        print("Stereo integration and modern UI features are working correctly:")
        print("  ✓ Stereo audio loading and processing")
        print("  ✓ Channel separation and convolution")
        print("  ✓ Modern Material Design UI components")
        print("  ✓ Enhanced visualization with stereo support")
        print("  ✓ Main application integration")
        print("\nYou can now run: python IR-Alchemist.py")
    else:
        print("❌ Some tests failed!")
        print("Please check the errors above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
