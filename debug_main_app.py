#!/usr/bin/env python3
"""
Debug script to identify issues with the main IR-Alchemist application.
"""

import sys
import traceback

def test_imports():
    """Test all imports step by step."""
    print("=== Testing Imports ===")
    
    try:
        print("1. Testing basic imports...")
        import os
        import sys
        import tempfile
        import soundfile as sf
        import numpy as np
        from scipy.signal import butter, lfilter, fftconvolve
        print("   ✓ Basic imports successful")
    except Exception as e:
        print(f"   ✗ Basic imports failed: {e}")
        return False
    
    try:
        print("2. Testing PyQt5 imports...")
        from PyQt5.QtWidgets import (
            QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
            QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
            QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect
        )
        from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot
        from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
        from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap
        print("   ✓ PyQt5 imports successful")
    except Exception as e:
        print(f"   ✗ PyQt5 imports failed: {e}")
        return False
    
    try:
        print("3. Testing matplotlib imports...")
        import matplotlib
        matplotlib.use('Agg')  # Safe backend
        import matplotlib.pyplot as plt
        print("   ✓ Matplotlib basic imports successful")
        
        # Try Qt5Agg backend
        try:
            matplotlib.use('Qt5Agg')
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            print("   ✓ Matplotlib Qt5Agg backend successful")
            matplotlib_available = True
        except Exception as e:
            print(f"   ⚠ Matplotlib Qt5Agg backend failed: {e}")
            matplotlib.use('Agg')
            matplotlib_available = False
            
    except Exception as e:
        print(f"   ✗ Matplotlib imports failed: {e}")
        matplotlib_available = False
    
    try:
        print("4. Testing custom imports...")
        from irpkg_reader import IRPKGReader
        print("   ✓ IRPKGReader import successful")
    except Exception as e:
        print(f"   ✗ IRPKGReader import failed: {e}")
        return False
    
    return True

def test_ir_exporter():
    """Test the IRExporter class."""
    print("\n=== Testing IRExporter ===")
    
    try:
        # Import the class definition from the main file
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_app", "IR-Alchemist.py")
        main_module = importlib.util.module_from_spec(spec)
        
        print("1. Loading main application module...")
        spec.loader.exec_module(main_module)
        print("   ✓ Main module loaded successfully")
        
        print("2. Testing IRExporter initialization...")
        exporter = main_module.IRExporter()
        print(f"   ✓ IRExporter created with {len(exporter.ir_reader.irs)} IRs")
        
        print("3. Testing IR generation...")
        irs = exporter.generate_multiple_irs(count=2, style="Random")
        print(f"   ✓ Generated {len(irs)} IRs successfully")
        
        return True
        
    except Exception as e:
        print(f"   ✗ IRExporter test failed: {e}")
        traceback.print_exc()
        return False

def test_gui_creation():
    """Test GUI creation."""
    print("\n=== Testing GUI Creation ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        print("1. Creating QApplication...")
        app = QApplication(sys.argv)
        print("   ✓ QApplication created successfully")
        
        print("2. Testing font loading...")
        from PyQt5.QtGui import QFontDatabase, QFont
        font_path = "Exo-VariableFont_wght.ttf"
        if os.path.exists(font_path):
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id != -1:
                families = QFontDatabase.applicationFontFamilies(font_id)
                if families:
                    print(f"   ✓ Font loaded: {families[0]}")
                else:
                    print("   ⚠ Font families not found")
            else:
                print("   ⚠ Font loading failed")
        else:
            print("   ⚠ Font file not found")
        
        print("3. Testing main window creation...")
        # Import the main window class
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_app", "IR-Alchemist.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        window = main_module.IRGeneratorGUI()
        print("   ✓ Main window created successfully")
        
        return True
        
    except Exception as e:
        print(f"   ✗ GUI creation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    print("IR-Alchemist Main Application Debug")
    print("=" * 50)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test IRExporter
    if not test_ir_exporter():
        success = False
    
    # Test GUI creation
    if not test_gui_creation():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The main application should work.")
        print("Try running: python IR-Alchemist.py")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
